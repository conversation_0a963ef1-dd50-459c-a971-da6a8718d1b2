# Service Configuration
SERVICE_NAME=voice-gateway
DEBUG=false
HOST=0.0.0.0
PORT=8002

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=voice-gateway

# Security Configuration
SECURITY_SECRET_KEY=dev-secret-key-change-in-production-please
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30
SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7

# Provider Selection Configuration
STT_PROVIDER=whisper
TTS_PROVIDER=openai
TRANSLATION_PROVIDER=huggingface
VAD_PROVIDER=webrtc

# Whisper STT Configuration
WHISPER_MODEL_SIZE=base.en
WHISPER_COMPUTE_TYPE=int8
WHISPER_DEVICE=cpu

# DeepGram STT/TTS Configuration
DEEPGRAM_API_KEY=your_deepgram_api_key_here
DEEPGRAM_MODEL=nova-3
DEEPGRAM_LANGUAGE=en-US
DEEPGRAM_TTS_MODEL=aura-2-thalia-en

# VAD Configuration
VAD_AGGRESSIVENESS=3
VAD_FRAME_DURATION_MS=30

# Translation Configuration
TRANSLATION_MODEL=Helsinki-NLP/opus-mt-en-es
TRANSLATION_DEVICE=cpu

# OpenAI TTS Configuration
TTS_MODEL=tts-1
TTS_VOICE=alloy
TTS_API_KEY=your_openai_api_key_here

# Audio Processing Configuration
AUDIO_SAMPLE_RATE=16000
AUDIO_CHUNK_SIZE=1024
AUDIO_CHANNELS=1

# WebSocket Configuration
WS_MAX_CONNECTIONS=100
WS_HEARTBEAT_INTERVAL=30
WS_CONNECTION_TIMEOUT=300

# External Services
CALL_DATA_SERVICE_URL=http://localhost:8003
AUTH_SERVICE_URL=http://localhost:8001

# ARQ Task Queue Configuration
ARQ_REDIS_SETTINGS=redis://localhost:6379/1
ARQ_MAX_JOBS=10
ARQ_JOB_TIMEOUT=300

# Tracing Configuration
TRACING_ENABLED=true
TRACING_OTLP_ENDPOINT=http://tempo:4317