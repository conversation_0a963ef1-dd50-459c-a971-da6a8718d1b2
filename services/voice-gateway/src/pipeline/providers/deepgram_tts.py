"""
DeepGram TTS provider implementation.
"""

from cortexacommon.logging import get_logger
from typing import Optional, Dict, Any

from .base import BaseTTSProvider
from ...core.config import settings

logger = get_logger(__name__)


class DeepGramTTSProvider(BaseTTSProvider):
    """Text-to-Speech provider using DeepGram Aura API."""

    def __init__(self, 
                 api_key: Optional[str] = None,
                 model: Optional[str] = None,
                 **kwargs):
        """
        Initialize DeepGram TTS provider.
        
        Args:
            api_key: DeepGram API key
            model: DeepGram TTS model identifier (e.g., "aura-2-thalia-en")
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.api_key = api_key or settings.deepgram_api_key
        self.model = model or "aura-2-thalia-en"  # Default DeepGram TTS model
        self._client: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize DeepGram TTS client."""
        if not self.api_key:
            logger.error("DeepGram API key not provided for TTS")
            self._client = None
            self._initialized = False
            return

        try:
            from deepgram import DeepgramClient

            self._client = DeepgramClient(api_key=self.api_key)
            self._initialized = True
            logger.info(f"DeepGram TTS client initialized with model {self.model}")
        except ImportError:
            logger.error("deepgram-sdk not available for TTS")
            self._client = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize DeepGram TTS client: {e}")
            self._client = None
            self._initialized = False

    async def synthesize(self, text: str) -> Optional[bytes]:
        """
        Synthesize speech from text using DeepGram Aura API.

        Args:
            text: Text to synthesize

        Returns:
            Audio bytes or None if synthesis fails
        """
        if not text.strip() or self._client is None:
            return None

        try:
            from deepgram import SpeakOptions

            # Configure TTS options
            options = SpeakOptions(
                model=self.model,
                encoding="linear16",
                sample_rate=16000,
            )

            # Generate speech
            response = await self._client.speak.rest.v("1").save(
                filename=None,  # Return bytes instead of saving to file
                source={"text": text},
                options=options
            )

            if response and hasattr(response, 'content'):
                return response.content

            return None

        except Exception as e:
            logger.error(f"DeepGram TTS error: {e}")
            return None

    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the DeepGram TTS provider."""
        base_health = await super().health_check()
        base_health.update({
            "model": self.model,
            "api_key_configured": bool(self.api_key)
        })
        return base_health
