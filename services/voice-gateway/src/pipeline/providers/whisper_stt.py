"""
Whisper STT provider implementation.
"""

import asyncio
from cortexacommon.logging import get_logger
import numpy as np
from typing import <PERSON>ple, Optional

from .base import BaseSTTProvider
from ...core.config import settings

logger = get_logger(__name__)


class WhisperSTTProvider(BaseSTTProvider):
    """Speech-to-Text provider using Faster Whisper."""
    
    def __init__(self, 
                 model_size: Optional[str] = None,
                 compute_type: Optional[str] = None,
                 device: Optional[str] = None,
                 **kwargs):
        """
        Initialize Whisper STT provider.
        
        Args:
            model_size: Whisper model size (tiny, base, small, medium, large)
            compute_type: Compute type (int8, int16, float16, float32)
            device: Device for model (cpu, cuda)
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.model_size = model_size or settings.whisper_model_size
        self.compute_type = compute_type or settings.whisper_compute_type
        self.device = device or settings.whisper_device
        self._model: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize Faster Whisper model."""
        try:
            from faster_whisper import WhisperModel
            
            # Run model initialization in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self._model = await loop.run_in_executor(
                None,
                lambda: WhisperModel(
                    self.model_size,
                    device=self.device,
                    compute_type=self.compute_type,
                )
            )
            self._initialized = True
            logger.info(f"Whisper model {self.model_size} initialized on {self.device}")
        except ImportError:
            logger.error("faster-whisper not available")
            self._model = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize Whisper model: {e}")
            self._model = None
            self._initialized = False
    
    async def transcribe(self, audio_data: bytes) -> Tuple[str, float]:
        """
        Transcribe audio to text.

        Args:
            audio_data: Audio data bytes

        Returns:
            Tuple of (transcribed_text, confidence_score)
        """
        if self._model is None:
            return "Transcription not available", 0.0

        with self.create_span("whisper_stt.transcribe",
                             audio_size_bytes=len(audio_data),
                             model=self.model_size) as span:
            try:
                # Convert bytes to numpy array
                audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0

                self.add_span_event(span, "audio_converted",
                                   audio_duration_seconds=len(audio_np) / 16000)

                # Run transcription in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                segments, info = await loop.run_in_executor(
                    None,
                    lambda: self._model.transcribe(audio_np, language="en")
                )

                self.add_span_event(span, "transcription_completed")

                # Combine segments into single text
                text_parts = []
                total_confidence = 0.0
                segment_count = 0

                for segment in segments:
                    text_parts.append(segment.text.strip())
                    # Convert log probability to confidence score (0.0 to 1.0)
                    # avg_logprob is typically negative, so we use exp() to convert to probability
                    # and clamp to [0.0, 1.0] range
                    avg_logprob = getattr(segment, 'avg_logprob', -1.0)
                    segment_confidence = max(0.0, min(1.0, np.exp(avg_logprob)))
                    total_confidence += segment_confidence
                    segment_count += 1

                text = " ".join(text_parts).strip()
                confidence = total_confidence / segment_count if segment_count > 0 else 0.0

                span.set_attribute("transcription.text_length", len(text))
                span.set_attribute("transcription.confidence", confidence)
                span.set_attribute("transcription.segment_count", segment_count)

                return text, confidence

            except Exception as e:
                self.set_span_error(span, e)
                logger.error(f"STT error: {e}")
                return "", 0.0
