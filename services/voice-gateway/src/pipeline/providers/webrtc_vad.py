"""
WebRTC VAD provider implementation.
"""

from cortexacommon.logging import get_logger
from typing import Optional

from .base import BaseVADProvider
from ...core.config import settings

logger = get_logger(__name__)


class WebRTCVADProvider(BaseVADProvider):
    """Voice Activity Detection provider using WebRTC VAD."""
    
    def __init__(self, aggressiveness: int = 3, frame_duration_ms: int = 30, **kwargs):
        """
        Initialize WebRTC VAD provider.
        
        Args:
            aggressiveness: VAD aggressiveness level (0-3)
            frame_duration_ms: Frame duration in milliseconds
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.aggressiveness = aggressiveness
        self.frame_duration_ms = frame_duration_ms
        self._frame_size = int(settings.audio_sample_rate * frame_duration_ms / 1000)
        self._vad: Optional[object] = None
    
    async def initialize(self) -> None:
        """Initialize WebRTC VAD."""
        try:
            import webrtcvad
            self._vad = webrtcvad.Vad(self.aggressiveness)
            self._initialized = True
            logger.info(f"WebRTC VAD initialized with aggressiveness {self.aggressiveness}")
        except ImportError:
            logger.error("webrtcvad not available, VAD disabled")
            self._vad = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize WebRTC VAD: {e}")
            self._vad = None
            self._initialized = False
    
    def is_speech(self, audio_frame: bytes) -> bool:
        """
        Detect if audio frame contains speech.
        
        Args:
            audio_frame: Audio frame bytes (16kHz, 16-bit, mono)
            
        Returns:
            bool: True if speech detected
        """
        if self._vad is None:
            return True  # Assume speech if VAD not available
        
        try:
            # Ensure frame is correct size
            if len(audio_frame) != self.frame_size * 2:  # 2 bytes per sample
                return False
            
            return self._vad.is_speech(audio_frame, settings.audio_sample_rate)
        except Exception as e:
            logger.warning(f"VAD error: {e}")
            return True  # Assume speech on error
    
    @property
    def frame_size(self) -> int:
        """Get the required frame size for VAD processing."""
        return self._frame_size
