from typing import Optional
from pydantic import Field
from pydantic_settings import SettingsConfigDict

from cortexacommon.config import BaseServiceSettings


class VoiceGatewaySettings(BaseServiceSettings):
    """Voice Gateway service-specific settings."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )
    
    # Service identification
    service_name: str = Field(default="voice-gateway", description="Service name")
    port: int = Field(default=8002, description="Service port")
    
    # Provider Selection Configuration
    stt_provider: str = Field(
        default="whisper",
        description="STT provider (whisper, deepgram, etc.)"
    )
    tts_provider: str = Field(
        default="openai",
        description="TTS provider (openai, deepgram, etc.)"
    )
    translation_provider: str = Field(
        default="huggingface",
        description="Translation provider (huggingface, google, etc.)"
    )
    vad_provider: str = Field(
        default="webrtc",
        description="VAD provider (webrtc, etc.)"
    )

    # Whisper STT Configuration
    whisper_model_size: str = Field(
        default="base.en",
        description="Whisper model size (tiny, base, small, medium, large)"
    )
    whisper_compute_type: str = Field(
        default="int8",
        description="Whisper compute type (int8, int16, float16, float32)"
    )
    whisper_device: str = Field(
        default="cpu",
        description="Device for Whisper model (cpu, cuda)"
    )

    # DeepGram STT Configuration
    deepgram_api_key: Optional[str] = Field(
        default=None,
        description="DeepGram API key for STT/TTS"
    )
    deepgram_model: str = Field(
        default="nova-3",
        description="DeepGram STT model identifier"
    )
    deepgram_language: str = Field(
        default="en-US",
        description="DeepGram language code"
    )

    # DeepGram TTS Configuration
    deepgram_tts_model: str = Field(
        default="aura-2-thalia-en",
        description="DeepGram TTS model identifier"
    )

    # VAD Configuration
    vad_aggressiveness: int = Field(
        default=3,
        ge=0,
        le=3,
        description="VAD aggressiveness level (0-3)"
    )
    vad_frame_duration_ms: int = Field(
        default=30,
        description="VAD frame duration in milliseconds"
    )

    # Translation Configuration
    translation_model: str = Field(
        default="Helsinki-NLP/opus-mt-en-es",
        description="Translation model identifier"
    )
    translation_device: str = Field(
        default="cpu",
        description="Device for translation model (cpu, cuda)"
    )

    # OpenAI TTS Configuration
    tts_model: str = Field(
        default="tts-1",
        description="TTS model identifier"
    )
    tts_voice: str = Field(
        default="alloy",
        description="TTS voice identifier"
    )
    tts_api_key: Optional[str] = Field(
        default=None,
        description="API key for TTS provider"
    )
    
    # Audio Processing Configuration
    audio_sample_rate: int = Field(
        default=16000,
        description="Audio sample rate in Hz"
    )
    audio_chunk_size: int = Field(
        default=1024,
        description="Audio chunk size for processing"
    )
    audio_channels: int = Field(
        default=1,
        description="Number of audio channels"
    )
    
    # WebSocket Configuration
    ws_max_connections: int = Field(
        default=100,
        description="Maximum concurrent WebSocket connections"
    )
    ws_heartbeat_interval: int = Field(
        default=30,
        description="WebSocket heartbeat interval in seconds"
    )
    ws_connection_timeout: int = Field(
        default=300,
        description="WebSocket connection timeout in seconds"
    )
    
    # External Services
    call_data_service_url: str = Field(
        default="http://localhost:8003",
        description="Call Data Service URL"
    )
    auth_service_url: str = Field(
        default="http://localhost:8001",
        description="Auth Service URL"
    )
    
    # ARQ Task Queue Configuration
    arq_redis_settings: str = Field(
        default="redis://localhost:6379/1",
        description="Redis connection string for ARQ"
    )
    arq_max_jobs: int = Field(
        default=10,
        description="Maximum concurrent ARQ jobs"
    )
    arq_job_timeout: int = Field(
        default=300,
        description="ARQ job timeout in seconds"
    )

    enable_metrics: bool = Field(
        default=False,
        description="Enable Prometheus metrics"
    )


# Global settings instance
settings = VoiceGatewaySettings()
