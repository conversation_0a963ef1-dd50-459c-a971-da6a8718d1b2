"""
OpenTelemetry tracing configuration for voice-gateway.

This module sets up distributed tracing to monitor performance and latency
throughout the voice processing pipeline.
"""

from typing import Optional
from contextlib import contextmanager

from opentelemetry import trace
from opentelemetry.trace import Status, StatusCode

from cortexacommon.monitoring import setup_tracing as common_setup_tracing, get_tracer as common_get_tracer
from cortexacommon.monitoring.config import get_monitoring_settings
from cortexacommon.logging import get_logger

from .config import settings

logger = get_logger(__name__)


def setup_tracing() -> Optional[trace.Tracer]:
    """
    Initialize tracing via cortexacommon monitoring (Tempo/OTLP), honoring TRACING_* env vars.
    """
    try:
        monitoring_settings = get_monitoring_settings(settings.service_name)
        tracer = common_setup_tracing(monitoring_settings.tracing)
        if tracer:
            logger.info("Tracing initialized via cortexacommon")
        else:
            logger.info("Tracing disabled")
        return tracer
    except Exception as e:
        logger.error(f"Failed to initialize tracing: {e}")
        return None


def get_tracer() -> trace.Tracer:
    """Get a tracer from cortexacommon."""
    return common_get_tracer(__name__) or trace.get_tracer(__name__)


class TracingMixin:
    """Mixin class to add tracing capabilities to other classes."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._tracer = get_tracer()
    
    def create_span(self, name: str, **attributes):
        """
        Create a new span with the given name and attributes.

        Args:
            name: Span name
            **attributes: Span attributes

        Returns:
            Context manager yielding a started span as current span
        """
        @contextmanager
        def _span_cm():
            with self._tracer.start_as_current_span(name) as span:
                if attributes:
                    for key, value in attributes.items():
                        span.set_attribute(key, value)
                yield span
        return _span_cm()
    
    def add_span_event(self, span: trace.Span, name: str, **attributes):
        """
        Add an event to a span.
        
        Args:
            span: Span to add event to
            name: Event name
            **attributes: Event attributes
        """
        span.add_event(name, attributes)
    
    def set_span_error(self, span: trace.Span, error: Exception):
        """
        Mark a span as having an error.

        Args:
            span: Span to mark as error
            error: Exception that occurred
        """
        span.set_status(Status(StatusCode.ERROR, str(error)))
        span.set_attribute("error", True)
        span.set_attribute("error.type", type(error).__name__)
        span.set_attribute("error.message", str(error))


# Global tracer instance
_tracer: Optional[trace.Tracer] = None


def init_tracing():
    """Initialize tracing and store global tracer."""
    global _tracer
    _tracer = setup_tracing()


def get_global_tracer() -> Optional[trace.Tracer]:
    """Get the global tracer instance."""
    return _tracer
