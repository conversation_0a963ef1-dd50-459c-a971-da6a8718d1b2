from typing import Op<PERSON>
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

from cortexacommon.security import verify_token
from cortexacommon.security.auth import TokenData
from cortexacommon.config import SecuritySettings


async def get_current_user_ws(
    token: Optional[str] = None,
) -> TokenData:
    """
    Get current user from WebSocket JWT token.

    Since WebSocket connections from browsers cannot use Authorization headers,
    the JWT token must be passed as a query parameter.

    Args:
        token: JWT access token from query parameter

    Returns:
        TokenData: Decoded token data with user information

    Raises:
        HTTPException: If token is missing or invalid
    """
    if token is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Access token is required for WebSocket connection",
        )

    try:
        # Use the same token verification as other services
        security_settings = SecuritySettings()
        print(token)
        token_data = verify_token(token, security_settings, token_type="access")
        return token_data
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired access token",
        )


async def verify_user_permissions(
    user: TokenData,
    required_permissions: Optional[list[str]] = None,
) -> bool:
    """
    Verify user has required permissions for voice translation.
    
    Args:
        user: Token data with user information
        required_permissions: List of required permissions
        
    Returns:
        bool: True if user has required permissions
    """
    # For now, allow all authenticated users to use voice translation
    # In the future, this could check specific permissions based on user role
    if required_permissions is None:
        return True
    
    # Example permission checking logic
    user_role = user.role
    if user_role in ["ADMIN", "MANAGER", "OPERATOR"]:
        return True
    
    return False


class WebSocketConnectionManager:
    """Manages WebSocket connections and authentication state."""
    
    def __init__(self):
        self.active_connections: dict[str, dict] = {}
    
    def add_connection(self, call_id: str, websocket, user: TokenData):
        """Add a new WebSocket connection."""
        self.active_connections[call_id] = {
            "websocket": websocket,
            "user": user,
            "authenticated": True,
        }
    
    def remove_connection(self, call_id: str):
        """Remove a WebSocket connection."""
        if call_id in self.active_connections:
            del self.active_connections[call_id]
    
    def get_connection(self, call_id: str) -> Optional[dict]:
        """Get connection information by call ID."""
        return self.active_connections.get(call_id)
    
    def is_authenticated(self, call_id: str) -> bool:
        """Check if connection is authenticated."""
        connection = self.get_connection(call_id)
        return connection is not None and connection.get("authenticated", False)
    
    def get_active_connection_count(self) -> int:
        """Get number of active connections."""
        return len(self.active_connections)


# Global connection manager instance
connection_manager = WebSocketConnectionManager()
