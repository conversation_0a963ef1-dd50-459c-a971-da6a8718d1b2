"""
Event schemas for Kafka message publishing.

This module defines structured schemas for events published to Kafka
for consumption by other services in the Cortexa platform.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class BaseEvent(BaseModel):
    """Base event schema with common fields."""
    
    event_id: UUID = Field(default_factory=uuid4, description="Unique event identifier")
    event_type: str = Field(description="Type of the event")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Event timestamp")
    service: str = Field(default="voice-gateway", description="Source service")
    version: str = Field(default="1.0", description="Event schema version")
    correlation_id: Optional[str] = Field(default=None, description="Correlation ID for request tracing")


class CallStartedEvent(BaseEvent):
    """Event published when a voice call starts."""
    
    event_type: str = Field(default="call.started", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    user_id: str = Field(description="User identifier")
    source_language: Optional[str] = Field(default=None, description="Source language code")
    target_language: Optional[str] = Field(default=None, description="Target language code")
    providers: Dict[str, str] = Field(description="Selected providers for STT, TTS, translation")


class CallEndedEvent(BaseEvent):
    """Event published when a voice call ends."""
    
    event_type: str = Field(default="call.ended", description="Event type")
    call_id: str = Field(description="Unique call identifier")
    user_id: str = Field(description="User identifier")
    duration_seconds: float = Field(description="Call duration in seconds")
    total_segments: int = Field(description="Total speech segments processed")
    total_audio_received_bytes: int = Field(description="Total audio bytes received")
    total_audio_sent_bytes: int = Field(description="Total audio bytes sent")
    error_count: int = Field(description="Number of errors during the call")
    final_transcript: List[Dict[str, Any]] = Field(description="Complete call transcript")


class TranscriptionEvent(BaseEvent):
    """Event published for each transcription result."""
    
    event_type: str = Field(default="transcription.completed", description="Event type")
    call_id: str = Field(description="Call identifier")
    segment_id: str = Field(description="Speech segment identifier")
    original_text: str = Field(description="Original transcribed text")
    translated_text: str = Field(description="Translated text")
    confidence_score: float = Field(description="STT confidence score", ge=0.0, le=1.0)
    processing_time_ms: int = Field(description="Total processing time in milliseconds")
    audio_duration_ms: int = Field(description="Audio segment duration in milliseconds")
    providers_used: Dict[str, str] = Field(description="Providers used for processing")
    language_detected: Optional[str] = Field(default=None, description="Detected source language")


class ProcessingMetricsEvent(BaseEvent):
    """Event published with processing performance metrics."""
    
    event_type: str = Field(default="processing.metrics", description="Event type")
    call_id: str = Field(description="Call identifier")
    segment_id: str = Field(description="Speech segment identifier")
    metrics: Dict[str, Any] = Field(description="Processing metrics")
    
    class Config:
        schema_extra = {
            "example": {
                "call_id": "call_123",
                "segment_id": "segment_456",
                "metrics": {
                    "stt_duration_ms": 250,
                    "translation_duration_ms": 50,
                    "tts_duration_ms": 800,
                    "total_duration_ms": 1100,
                    "audio_size_bytes": 32000,
                    "output_audio_size_bytes": 28000,
                    "stt_provider": "whisper",
                    "tts_provider": "openai",
                    "translation_provider": "huggingface"
                }
            }
        }


class ErrorEvent(BaseEvent):
    """Event published when processing errors occur."""
    
    event_type: str = Field(default="processing.error", description="Event type")
    call_id: str = Field(description="Call identifier")
    error_type: str = Field(description="Type of error")
    error_message: str = Field(description="Error message")
    component: str = Field(description="Component where error occurred")
    segment_id: Optional[str] = Field(default=None, description="Speech segment identifier if applicable")
    stack_trace: Optional[str] = Field(default=None, description="Error stack trace")
    context: Dict[str, Any] = Field(default_factory=dict, description="Additional error context")


class ProviderSwitchEvent(BaseEvent):
    """Event published when switching between providers due to failures."""
    
    event_type: str = Field(default="provider.switched", description="Event type")
    call_id: str = Field(description="Call identifier")
    provider_type: str = Field(description="Type of provider (stt, tts, translation)")
    from_provider: str = Field(description="Previous provider")
    to_provider: str = Field(description="New provider")
    reason: str = Field(description="Reason for switch")
    failure_count: int = Field(description="Number of failures before switch")


class QualityMetricsEvent(BaseEvent):
    """Event published with quality metrics for monitoring."""
    
    event_type: str = Field(default="quality.metrics", description="Event type")
    call_id: str = Field(description="Call identifier")
    window_start: datetime = Field(description="Metrics window start time")
    window_end: datetime = Field(description="Metrics window end time")
    metrics: Dict[str, Any] = Field(description="Quality metrics")
    
    class Config:
        schema_extra = {
            "example": {
                "call_id": "call_123",
                "metrics": {
                    "average_stt_confidence": 0.85,
                    "segments_processed": 25,
                    "error_rate": 0.02,
                    "average_processing_latency_ms": 1200,
                    "audio_quality_score": 0.9,
                    "translation_accuracy_estimate": 0.88
                }
            }
        }


class SystemHealthEvent(BaseEvent):
    """Event published with system health information."""
    
    event_type: str = Field(default="system.health", description="Event type")
    active_connections: int = Field(description="Number of active connections")
    total_calls_today: int = Field(description="Total calls processed today")
    provider_status: Dict[str, bool] = Field(description="Provider availability status")
    system_resources: Dict[str, Any] = Field(description="System resource usage")
    performance_metrics: Dict[str, float] = Field(description="Performance metrics")


# Event type mapping for easy access
EVENT_TYPES = {
    "call.started": CallStartedEvent,
    "call.ended": CallEndedEvent,
    "transcription.completed": TranscriptionEvent,
    "processing.metrics": ProcessingMetricsEvent,
    "processing.error": ErrorEvent,
    "provider.switched": ProviderSwitchEvent,
    "quality.metrics": QualityMetricsEvent,
    "system.health": SystemHealthEvent,
}
