import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from cortexacommon.monitoring import setup_monitoring
from cortexacommon.logging import get_logger, setup_service_logging

from src.api.v1.router import api_router
from src.core.config import settings
from src.core.tracing import init_tracing
from src.core.metrics import setup_metrics
from src.pipeline.state import connection_state_manager
from src.pipeline.s2st import S2STProcessor

# Setup common logging/monitoring
setup_service_logging(service_name=settings.service_name, level="INFO")
setup_monitoring(service_name=settings.service_name, app=None)
logger = get_logger(__name__)

# Background task for connection cleanup
cleanup_task: asyncio.Task = None

# Global S2ST processor instance
s2st_processor: S2STProcessor = None


async def cleanup_idle_connections():
    """Background task to clean up idle connections."""
    while True:
        try:
            # Clean up connections idle for more than the configured timeout
            idle_call_ids = await connection_state_manager.cleanup_idle_connections(
                timeout=settings.ws_connection_timeout
            )
            
            if idle_call_ids:
                logger.info(f"Cleaned up {len(idle_call_ids)} idle connections", 
                           call_ids=idle_call_ids)
            
            # Sleep for 1 minute before next cleanup
            await asyncio.sleep(60)
            
        except Exception as e:
            logger.error(f"Error in connection cleanup task: {e}")
            await asyncio.sleep(60)  # Continue after error


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for startup and shutdown events.
    
    This handles:
    - Starting background tasks
    - Initializing resources
    - Cleaning up on shutdown
    """
    # Startup
    logger.info("Starting Voice Gateway service",
                service=settings.service_name,
                port=settings.port)

    # Initialize tracing
    init_tracing()

    # Start background cleanup task
    global cleanup_task
    cleanup_task = asyncio.create_task(cleanup_idle_connections())

    # Initialize S2ST processor
    global s2st_processor
    s2st_processor = S2STProcessor()
    try:
        await s2st_processor.initialize()
        logger.info("S2ST processor initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize S2ST processor: {e}")
        # Continue startup even if S2ST fails - individual connections will handle errors

    logger.info("Voice Gateway service started successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Voice Gateway service")
    
    # Cancel background tasks
    if cleanup_task:
        cleanup_task.cancel()
        try:
            await cleanup_task
        except asyncio.CancelledError:
            pass
    
    # Clean up all active connections
    active_connections = await connection_state_manager.get_all_connections()
    if active_connections:
        logger.info(f"Cleaning up {len(active_connections)} active connections")
        for state in active_connections:
            try:
                await state.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up connection {state.call_id}: {e}")
    
    logger.info("Voice Gateway service shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="Cortexa Voice Gateway Service",
    description="Real-time voice translation service for the Cortexa platform",
    version="0.1.0",
    openapi_url="/api/v1/openapi.json",
    docs_url="/api/v1/docs",
    redoc_url="/api/v1/redoc",
    lifespan=lifespan,
)

# Set up metrics
setup_metrics(app)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "Cortexa Voice Gateway",
        "version": "0.1.0",
        "description": "Real-time voice translation service",
        "status": "running",
    }


@app.get("/health")
async def health_check():
    """Health check endpoint with S2ST processor status."""
    connection_count = await connection_state_manager.get_connection_count()

    # Get S2ST processor health status
    s2st_status = None
    ready_for_calls = False

    if s2st_processor:
        s2st_status = await s2st_processor.get_health_status()
        ready_for_calls = s2st_processor.is_initialized

    return {
        "status": "healthy" if ready_for_calls else "initializing",
        "ready": ready_for_calls,
        "service": settings.service_name,
        "version": "0.1.0",
        "active_connections": connection_count,
        "max_connections": settings.ws_max_connections,
        "s2st_processor": s2st_status,
        "configuration": {
            "stt_provider": settings.stt_provider,
            "tts_provider": settings.tts_provider,
            "translation_provider": settings.translation_provider,
            "vad_provider": settings.vad_provider,
            "whisper_model": settings.whisper_model_size,
            "translation_model": settings.translation_model,
            "vad_aggressiveness": settings.vad_aggressiveness,
        }
    }


@app.get("/ready")
async def readiness_check():
    """Readiness check endpoint for Kubernetes/load balancer probes."""
    if not s2st_processor or not s2st_processor.is_initialized:
        return {"ready": False, "message": "S2ST processor not initialized"}, 503

    return {"ready": True, "message": "Service ready to accept calls"}


@app.get("/metrics")
async def metrics():
    """Metrics endpoint for monitoring."""
    connections = await connection_state_manager.get_all_connections()
    
    # Calculate aggregate metrics
    total_audio_received = sum(conn.total_audio_received for conn in connections)
    total_audio_sent = sum(conn.total_audio_sent for conn in connections)
    total_segments_processed = sum(conn.segments_processed for conn in connections)
    total_errors = sum(len(conn.errors) for conn in connections)
    
    return {
        "active_connections": len(connections),
        "max_connections": settings.ws_max_connections,
        "total_audio_received_bytes": total_audio_received,
        "total_audio_sent_bytes": total_audio_sent,
        "total_segments_processed": total_segments_processed,
        "total_errors": total_errors,
        "connections": [conn.get_stats() for conn in connections],
    }


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
    )
