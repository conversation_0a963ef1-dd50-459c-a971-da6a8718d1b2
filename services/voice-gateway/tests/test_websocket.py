import asyncio
import json
import pytest
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from fastapi import WebSocketDisconnect
from fastapi.testclient import Test<PERSON>lient

from src.api.v1.endpoints.call import websocket_endpoint
from src.pipeline.state import ConnectionState, CallState
from src.pipeline.tasks import (
    reader_task,
    writer_task,
    processor_task,
    heartbeat_task,
    handle_control_message,
    run_pipeline,
)


class TestWebSocketEndpoint:
    """Test suite for WebSocket endpoint."""

    @pytest.mark.asyncio
    async def test_websocket_connection_success(self, mock_websocket, mock_user):
        """Test successful WebSocket connection establishment."""
        call_id = "test-call-123"

        # Mock query parameters with valid token
        mock_websocket.query_params = {"token": "valid_token"}

        with patch("src.api.v1.endpoints.call.connection_state_manager") as mock_manager:
            mock_manager.add_connection = AsyncMock()
            mock_manager.remove_connection = AsyncMock()

            with patch("src.api.v1.endpoints.call.run_pipeline") as mock_run_pipeline:
                mock_run_pipeline.return_value = None

                with patch("src.api.v1.endpoints.call.get_current_user_ws") as mock_auth, \
                     patch("src.api.v1.endpoints.call.handle_call_termination") as mock_termination:
                    mock_auth.return_value = mock_user
                    mock_termination.return_value = None

                    await websocket_endpoint(mock_websocket, call_id)

                    # Verify WebSocket was accepted
                    mock_websocket.accept.assert_called_once()

                    # Verify authentication was called
                    mock_auth.assert_called_once_with(token="valid_token")

                    # Verify connection was added to manager
                    mock_manager.add_connection.assert_called_once()

                    # Verify initial connection message was sent
                    mock_websocket.send_json.assert_called()
                    call_args = mock_websocket.send_json.call_args[0][0]
                    assert call_args["type"] == "connection_established"
                    assert call_args["call_id"] == call_id

    @pytest.mark.asyncio
    async def test_websocket_disconnect_handling(self, mock_websocket, mock_user):
        """Test WebSocket disconnect handling."""
        call_id = "test-call-123"

        # Mock query parameters with valid token
        mock_websocket.query_params = {"token": "valid_token"}

        with patch("src.api.v1.endpoints.call.connection_state_manager") as mock_manager:
            mock_manager.add_connection = AsyncMock()
            mock_manager.remove_connection = AsyncMock()

            with patch("src.api.v1.endpoints.call.run_pipeline") as mock_run_pipeline:
                mock_run_pipeline.side_effect = WebSocketDisconnect()

                with patch("src.api.v1.endpoints.call.get_current_user_ws") as mock_auth, \
                     patch("src.api.v1.endpoints.call.persist_call_data") as mock_persist, \
                     patch("src.api.v1.endpoints.call.publish_call_ended_event") as mock_publish, \
                     patch("src.api.v1.endpoints.call.enqueue_analytics_task") as mock_enqueue:
                    mock_auth.return_value = mock_user
                    mock_persist.return_value = None
                    mock_publish.return_value = None
                    mock_enqueue.return_value = None

                    await websocket_endpoint(mock_websocket, call_id)

                    # Verify connection was removed from manager
                    mock_manager.remove_connection.assert_called_once_with(call_id)

    @pytest.mark.asyncio
    async def test_websocket_error_handling(self, mock_websocket, mock_user):
        """Test WebSocket error handling."""
        call_id = "test-call-123"

        # Mock query parameters with valid token
        mock_websocket.query_params = {"token": "valid_token"}

        with patch("src.api.v1.endpoints.call.connection_state_manager") as mock_manager:
            mock_manager.add_connection = AsyncMock()
            mock_manager.remove_connection = AsyncMock()

            with patch("src.api.v1.endpoints.call.run_pipeline") as mock_run_pipeline:
                mock_run_pipeline.side_effect = Exception("Pipeline error")

                with patch("src.api.v1.endpoints.call.get_current_user_ws") as mock_auth, \
                     patch("src.api.v1.endpoints.call.persist_call_data") as mock_persist, \
                     patch("src.api.v1.endpoints.call.publish_call_ended_event") as mock_publish, \
                     patch("src.api.v1.endpoints.call.enqueue_analytics_task") as mock_enqueue:
                    mock_auth.return_value = mock_user
                    mock_persist.return_value = None
                    mock_publish.return_value = None
                    mock_enqueue.return_value = None

                    await websocket_endpoint(mock_websocket, call_id)

                    # Verify connection was removed from manager
                    mock_manager.remove_connection.assert_called_once_with(call_id)

    @pytest.mark.asyncio
    async def test_websocket_authentication_failure(self, mock_websocket):
        """Test WebSocket authentication failure handling."""
        call_id = "test-call-123"

        # Mock query parameters with missing token
        mock_websocket.query_params = {}

        await websocket_endpoint(mock_websocket, call_id)

        # Verify WebSocket was accepted first
        mock_websocket.accept.assert_called_once()

        # Verify WebSocket was closed with authentication error
        mock_websocket.close.assert_called_once_with(code=4001, reason="Access token is required")

    @pytest.mark.asyncio
    async def test_websocket_invalid_token(self, mock_websocket):
        """Test WebSocket invalid token handling."""
        call_id = "test-call-123"

        # Mock query parameters with invalid token
        mock_websocket.query_params = {"token": "invalid_token"}

        with patch("src.api.v1.endpoints.call.get_current_user_ws") as mock_auth:
            mock_auth.side_effect = Exception("Invalid token")

            await websocket_endpoint(mock_websocket, call_id)

            # Verify WebSocket was accepted first
            mock_websocket.accept.assert_called_once()

            # Verify authentication was attempted
            mock_auth.assert_called_once_with(token="invalid_token")

            # Verify WebSocket was closed with authentication error
            mock_websocket.close.assert_called_once_with(code=4001, reason="Invalid or expired access token")


class TestReaderTask:
    """Test suite for reader task."""

    @pytest.mark.asyncio
    async def test_reader_task_audio_data(self, connection_state):
        """Test reader task processing audio data."""
        audio_data = b"fake_audio_data"
        
        # Mock WebSocket to return audio data then disconnect
        connection_state.websocket.receive_bytes = AsyncMock(side_effect=[audio_data, WebSocketDisconnect()])
        
        await reader_task(connection_state)
        
        # Verify audio was added to inbound queue
        assert not connection_state.inbound_queue.empty()
        received_audio = await connection_state.inbound_queue.get()
        assert received_audio == audio_data

    @pytest.mark.asyncio
    async def test_reader_task_control_message(self, connection_state):
        """Test reader task processing control messages."""
        control_message = {"type": "ping"}
        
        # Mock WebSocket to return control message then disconnect
        connection_state.websocket.receive_text = AsyncMock(side_effect=[json.dumps(control_message), WebSocketDisconnect()])
        connection_state.websocket.receive_bytes = AsyncMock(side_effect=WebSocketDisconnect())
        
        with patch("src.pipeline.tasks.handle_control_message") as mock_handle:
            mock_handle.return_value = None
            
            await reader_task(connection_state)
            
            # Verify control message was handled
            mock_handle.assert_called_once_with(connection_state, control_message)

    @pytest.mark.asyncio
    async def test_reader_task_websocket_disconnect(self, connection_state):
        """Test reader task handling WebSocket disconnect."""
        connection_state.websocket.receive_bytes = AsyncMock(side_effect=WebSocketDisconnect())
        
        await reader_task(connection_state)
        
        # Should complete without error
        assert connection_state.state == CallState.ENDING


class TestWriterTask:
    """Test suite for writer task."""

    @pytest.mark.asyncio
    async def test_writer_task_send_audio(self, connection_state):
        """Test writer task sending audio data."""
        audio_data = b"fake_tts_audio"
        
        # Add audio to outbound queue
        await connection_state.outbound_queue.put(audio_data)
        
        # Set state to ending to stop the task
        connection_state.state = CallState.ENDING
        
        await writer_task(connection_state)
        
        # Verify audio was sent via WebSocket
        connection_state.websocket.send_bytes.assert_called_once_with(audio_data)

    @pytest.mark.asyncio
    async def test_writer_task_websocket_error(self, connection_state):
        """Test writer task handling WebSocket send error."""
        audio_data = b"fake_tts_audio"
        
        # Add audio to outbound queue
        await connection_state.outbound_queue.put(audio_data)
        
        # Mock WebSocket send to raise error
        connection_state.websocket.send_bytes = AsyncMock(side_effect=Exception("Send error"))
        
        # Set state to ending to stop the task
        connection_state.state = CallState.ENDING
        
        await writer_task(connection_state)
        
        # Should handle error gracefully
        assert connection_state.state == CallState.ENDING


class TestProcessorTask:
    """Test suite for processor task."""

    @pytest.mark.asyncio
    async def test_processor_task_initialization(self, connection_state):
        """Test processor task initialization."""
        with patch("src.pipeline.tasks.S2STProcessor") as mock_processor_class:
            mock_processor = MagicMock()
            mock_processor.process_audio_chunk = AsyncMock()
            mock_processor_class.return_value = mock_processor
            
            # Set state to ending to stop the task quickly
            connection_state.state = CallState.ENDING
            
            await processor_task(connection_state)
            
            # Verify processor was created
            mock_processor_class.assert_called_once()

    @pytest.mark.asyncio
    async def test_processor_task_processing(self, connection_state):
        """Test processor task audio processing."""
        with patch("src.pipeline.tasks.S2STProcessor") as mock_processor_class:
            mock_processor = MagicMock()
            mock_processor.process_audio_chunk = AsyncMock()
            mock_processor_class.return_value = mock_processor
            
            # Add some audio to process
            await connection_state.inbound_queue.put(b"test_audio")
            
            # Set state to ending after a short delay
            async def set_ending():
                await asyncio.sleep(0.01)
                connection_state.state = CallState.ENDING
            
            # Run both tasks concurrently
            await asyncio.gather(
                processor_task(connection_state),
                set_ending(),
            )
            
            # Verify processor was called
            mock_processor.process_audio_chunk.assert_called()


class TestHeartbeatTask:
    """Test suite for heartbeat task."""

    @pytest.mark.asyncio
    async def test_heartbeat_task_ping_pong(self, connection_state):
        """Test heartbeat task ping/pong mechanism."""
        # Mock WebSocket ping/pong
        connection_state.websocket.ping = AsyncMock()
        
        # Set state to ending after a short delay
        async def set_ending():
            await asyncio.sleep(0.1)
            connection_state.state = CallState.ENDING
        
        # Run both tasks concurrently
        await asyncio.gather(
            heartbeat_task(connection_state),
            set_ending(),
        )
        
        # Verify ping was sent
        connection_state.websocket.ping.assert_called()

    @pytest.mark.asyncio
    async def test_heartbeat_task_connection_timeout(self, connection_state):
        """Test heartbeat task handling connection timeout."""
        # Mock WebSocket ping to raise timeout
        connection_state.websocket.ping = AsyncMock(side_effect=asyncio.TimeoutError())
        
        await heartbeat_task(connection_state)
        
        # Should set state to error on timeout
        assert connection_state.state == CallState.ERROR


class TestControlMessageHandling:
    """Test suite for control message handling."""

    @pytest.mark.asyncio
    async def test_handle_ping_message(self, connection_state):
        """Test handling ping control message."""
        message = {"type": "ping"}
        
        await handle_control_message(connection_state, message)
        
        # Should send pong response
        connection_state.websocket.send_json.assert_called_once()
        call_args = connection_state.websocket.send_json.call_args[0][0]
        assert call_args["type"] == "pong"

    @pytest.mark.asyncio
    async def test_handle_config_update_message(self, connection_state):
        """Test handling config update control message."""
        message = {
            "type": "config_update",
            "config": {
                "target_language": "es",
                "voice_speed": 1.2
            }
        }
        
        await handle_control_message(connection_state, message)
        
        # Should update connection config
        assert connection_state.config["target_language"] == "es"
        assert connection_state.config["voice_speed"] == 1.2

    @pytest.mark.asyncio
    async def test_handle_end_call_message(self, connection_state):
        """Test handling end call control message."""
        message = {"type": "end_call"}
        
        await handle_control_message(connection_state, message)
        
        # Should set state to ending
        assert connection_state.state == CallState.ENDING

    @pytest.mark.asyncio
    async def test_handle_unknown_message(self, connection_state):
        """Test handling unknown control message."""
        message = {"type": "unknown_message_type"}
        
        await handle_control_message(connection_state, message)
        
        # Should not change state or raise error
        assert connection_state.state == CallState.ACTIVE


class TestPipelineOrchestration:
    """Test suite for pipeline orchestration."""

    @pytest.mark.asyncio
    async def test_run_pipeline_task_creation(self, connection_state):
        """Test that run_pipeline creates all necessary tasks."""
        with patch("src.pipeline.tasks.reader_task") as mock_reader, \
             patch("src.pipeline.tasks.writer_task") as mock_writer, \
             patch("src.pipeline.tasks.processor_task") as mock_processor, \
             patch("src.pipeline.tasks.heartbeat_task") as mock_heartbeat:
            
            # Mock all tasks to complete quickly
            mock_reader.return_value = None
            mock_writer.return_value = None
            mock_processor.return_value = None
            mock_heartbeat.return_value = None
            
            # Set state to ending to stop pipeline quickly
            connection_state.state = CallState.ENDING
            
            await run_pipeline(connection_state)
            
            # Verify all tasks were called
            mock_reader.assert_called_once_with(connection_state)
            mock_writer.assert_called_once_with(connection_state)
            mock_processor.assert_called_once_with(connection_state)
            mock_heartbeat.assert_called_once_with(connection_state)

    @pytest.mark.asyncio
    async def test_run_pipeline_error_handling(self, connection_state):
        """Test pipeline error handling."""
        with patch("src.pipeline.tasks.reader_task") as mock_reader:
            mock_reader.side_effect = Exception("Reader task error")
            
            # Should handle error gracefully
            await run_pipeline(connection_state)
            
            # State should be set to error
            assert connection_state.state == CallState.ERROR

    @pytest.mark.asyncio
    async def test_run_pipeline_cleanup(self, connection_state):
        """Test pipeline cleanup on completion."""
        with patch("src.pipeline.tasks.reader_task") as mock_reader, \
             patch("src.pipeline.tasks.writer_task") as mock_writer, \
             patch("src.pipeline.tasks.processor_task") as mock_processor, \
             patch("src.pipeline.tasks.heartbeat_task") as mock_heartbeat:
            
            # Mock all tasks to complete quickly
            mock_reader.return_value = None
            mock_writer.return_value = None
            mock_processor.return_value = None
            mock_heartbeat.return_value = None
            
            connection_state.state = CallState.ENDING
            
            await run_pipeline(connection_state)
            
            # Verify cleanup was performed
            assert connection_state.state == CallState.ENDED
