import asyncio
import pytest
import wave
from pathlib import Path
from unittest.mock import MagicMock

from src.pipeline.s2st import S2STProcessor
from src.pipeline.state import ConnectionState, CallState


class TestAudioSampleProcessing:
    """Test suite for processing real audio samples."""

    def test_load_silence_audio_sample(self, silence_audio):
        """Test loading the silence audio sample."""
        assert isinstance(silence_audio, bytes)
        assert len(silence_audio) > 0
        
        # Should be a valid WAV file
        assert silence_audio.startswith(b'RIFF')
        assert b'WAVE' in silence_audio[:20]

    def test_load_speech_audio_sample(self, speech_audio):
        """Test loading the speech audio sample."""
        assert isinstance(speech_audio, bytes)
        assert len(speech_audio) > 0
        
        # Should be a valid WAV file
        assert speech_audio.startswith(b'RIFF')
        assert b'WAVE' in speech_audio[:20]

    def test_extract_audio_data_from_wav(self, speech_audio):
        """Test extracting raw audio data from WAV file."""
        # Write to temporary file for wave module
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
            tmp_file.write(speech_audio)
            tmp_file.flush()
            
            # Read with wave module
            with wave.open(tmp_file.name, 'rb') as wav_file:
                frames = wav_file.readframes(wav_file.getnframes())
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                
                assert sample_rate > 0
                assert channels > 0
                assert sample_width > 0
                assert len(frames) > 0
        
        # Clean up
        Path(tmp_file.name).unlink()


class TestVADWithRealAudio:
    """Test VAD processor with real audio samples."""

    def test_vad_with_silence_audio(self, silence_audio, mock_vad):
        """Test VAD processing with silence audio sample."""
        from src.pipeline.s2st import VADProcessor
        
        vad = VADProcessor()
        
        # Extract raw audio data (skip WAV header)
        # This is a simplified extraction - in real implementation,
        # you'd properly parse the WAV file
        raw_audio = silence_audio[44:]  # Skip typical WAV header
        
        # Process in chunks
        chunk_size = 960  # 30ms at 16kHz
        speech_detected = False
        
        for i in range(0, len(raw_audio) - chunk_size, chunk_size):
            chunk = raw_audio[i:i + chunk_size]
            if len(chunk) == chunk_size:
                # Mock VAD to return False for silence
                mock_vad.is_speech.return_value = False
                result = vad.is_speech(chunk)
                if result:
                    speech_detected = True
                    break
        
        # Should not detect speech in silence
        assert not speech_detected

    def test_vad_with_speech_audio(self, speech_audio, mock_vad):
        """Test VAD processing with speech audio sample."""
        from src.pipeline.s2st import VADProcessor
        
        vad = VADProcessor()
        
        # Extract raw audio data (skip WAV header)
        raw_audio = speech_audio[44:]  # Skip typical WAV header
        
        # Process in chunks
        chunk_size = 960  # 30ms at 16kHz
        speech_detected = False
        
        for i in range(0, len(raw_audio) - chunk_size, chunk_size):
            chunk = raw_audio[i:i + chunk_size]
            if len(chunk) == chunk_size:
                # Mock VAD to return True for speech
                mock_vad.is_speech.return_value = True
                result = vad.is_speech(chunk)
                if result:
                    speech_detected = True
                    break
        
        # Should detect speech in speech audio
        assert speech_detected


class TestS2STWithRealAudio:
    """Test complete S2ST pipeline with real audio samples."""

    @pytest.mark.asyncio
    async def test_s2st_pipeline_with_speech_audio(
        self, 
        speech_audio, 
        connection_state,
        mock_vad,
        mock_whisper,
        mock_translator,
        mock_openai_tts
    ):
        """Test complete S2ST pipeline with speech audio sample."""
        processor = S2STProcessor()
        
        # Mock all components for successful processing
        mock_vad.is_speech.return_value = True
        mock_whisper.transcribe.return_value = (
            [{"text": "I've got a drunk driver in front of me - I'm going to pull him over and see what's up", "start": 0.0, "end": 5.0}],
            {"language": "en", "language_probability": 0.95}
        )
        mock_translator.return_value = [{"translation_text": "Tengo un conductor ebrio frente a mí - voy a detenerlo y ver qué pasa"}]
        
        mock_response = MagicMock()
        mock_response.content = b"fake_spanish_tts_audio"
        mock_openai_tts.return_value.audio.speech.create.return_value = mock_response
        
        # Extract and add audio data to inbound queue
        raw_audio = speech_audio[44:]  # Skip WAV header
        chunk_size = 1024
        
        for i in range(0, min(len(raw_audio), chunk_size * 10), chunk_size):
            chunk = raw_audio[i:i + chunk_size]
            if chunk:
                await connection_state.inbound_queue.put(chunk)
        
        # Process audio chunks
        await processor.process_audio_chunk(connection_state)
        
        # Verify processing occurred
        assert connection_state.total_audio_received > 0

    @pytest.mark.asyncio
    async def test_s2st_pipeline_with_silence_audio(
        self,
        silence_audio,
        connection_state,
        mock_vad,
        mock_whisper,
        mock_translator,
        mock_openai_tts
    ):
        """Test S2ST pipeline with silence audio sample."""
        processor = S2STProcessor()
        
        # Mock VAD to detect no speech in silence
        mock_vad.is_speech.return_value = False
        
        # Extract and add audio data to inbound queue
        raw_audio = silence_audio[44:]  # Skip WAV header
        chunk_size = 1024
        
        for i in range(0, min(len(raw_audio), chunk_size * 5), chunk_size):
            chunk = raw_audio[i:i + chunk_size]
            if chunk:
                await connection_state.inbound_queue.put(chunk)
        
        # Process audio chunks
        await processor.process_audio_chunk(connection_state)
        
        # Should not trigger STT/translation/TTS for silence
        mock_whisper.transcribe.assert_not_called()
        mock_translator.assert_not_called()
        mock_openai_tts.return_value.audio.speech.create.assert_not_called()


class TestEndToEndWorkflow:
    """Test complete end-to-end voice translation workflow."""

    @pytest.mark.asyncio
    async def test_complete_voice_translation_workflow(
        self,
        speech_audio,
        mock_websocket,
        mock_user,
        mock_vad,
        mock_whisper,
        mock_translator,
        mock_openai_tts
    ):
        """Test complete voice translation workflow from WebSocket to TTS output."""
        
        # Setup mocks for successful translation
        mock_vad.is_speech.return_value = True
        mock_whisper.transcribe.return_value = (
            [{"text": "I've got a drunk driver in front of me", "start": 0.0, "end": 3.0}],
            {"language": "en", "language_probability": 0.95}
        )
        mock_translator.return_value = [{"translation_text": "Tengo un conductor ebrio frente a mí"}]
        
        mock_response = MagicMock()
        mock_response.content = b"translated_tts_audio_output"
        mock_openai_tts.return_value.audio.speech.create.return_value = mock_response
        
        # Create connection state
        connection_state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="integration-test-call",
            state=CallState.ACTIVE,
        )
        
        # Simulate receiving audio data
        raw_audio = speech_audio[44:]  # Skip WAV header
        chunk_size = 1024
        
        # Add audio chunks to inbound queue
        for i in range(0, min(len(raw_audio), chunk_size * 5), chunk_size):
            chunk = raw_audio[i:i + chunk_size]
            if chunk:
                await connection_state.inbound_queue.put(chunk)
        
        # Process the audio through S2ST pipeline
        processor = S2STProcessor()
        
        # Simulate speech detection and processing
        processor.speech_buffer.extend(raw_audio[:chunk_size * 3])  # Add some audio
        await processor._process_complete_segment(connection_state)
        
        # Verify the complete workflow
        assert mock_whisper.transcribe.called
        assert mock_translator.called
        assert mock_openai_tts.return_value.audio.speech.create.called
        
        # Verify TTS output was queued
        assert not connection_state.outbound_queue.empty()
        tts_output = await connection_state.outbound_queue.get()
        assert tts_output == b"translated_tts_audio_output"

    @pytest.mark.asyncio
    async def test_websocket_control_message_integration(
        self,
        mock_websocket,
        mock_user
    ):
        """Test WebSocket control message handling integration."""
        
        # Create connection state
        connection_state = ConnectionState(
            websocket=mock_websocket,
            user=mock_user,
            call_id="control-test-call",
            state=CallState.ACTIVE,
        )
        
        # Test ping/pong
        from src.pipeline.tasks import handle_control_message
        
        ping_message = {"type": "ping"}
        await handle_control_message(connection_state, ping_message)
        
        # Verify pong response
        mock_websocket.send_json.assert_called()
        call_args = mock_websocket.send_json.call_args[0][0]
        assert call_args["type"] == "pong"
        
        # Test configuration update
        config_message = {
            "type": "config_update",
            "config": {
                "target_language": "es",
                "voice_speed": 1.5
            }
        }
        await handle_control_message(connection_state, config_message)
        
        # Verify configuration was updated
        assert connection_state.config["target_language"] == "es"
        assert connection_state.config["voice_speed"] == 1.5

    @pytest.mark.asyncio
    async def test_error_recovery_integration(
        self,
        speech_audio,
        connection_state,
        mock_vad,
        mock_whisper,
        mock_translator,
        mock_openai_tts
    ):
        """Test error recovery in the integration workflow."""
        
        processor = S2STProcessor()
        
        # Setup mocks with some failures
        mock_vad.is_speech.return_value = True
        mock_whisper.transcribe.side_effect = Exception("STT service unavailable")
        
        # Add audio to process
        raw_audio = speech_audio[44:][:1024]
        await connection_state.inbound_queue.put(raw_audio)
        
        # Process should handle STT error gracefully
        await processor.process_audio_chunk(connection_state)
        
        # Verify error was logged
        assert len(connection_state.errors) > 0
        error = connection_state.errors[0]
        assert "STT" in error["message"] or "transcription" in error["message"].lower()

    @pytest.mark.asyncio
    async def test_concurrent_audio_processing(
        self,
        speech_audio,
        silence_audio,
        mock_websocket,
        mock_user,
        mock_vad,
        mock_whisper,
        mock_translator,
        mock_openai_tts
    ):
        """Test concurrent processing of multiple audio streams."""
        
        # Setup mocks
        mock_vad.is_speech.return_value = True
        mock_whisper.transcribe.return_value = (
            [{"text": "Test audio", "start": 0.0, "end": 1.0}],
            {"language": "en", "language_probability": 0.90}
        )
        mock_translator.return_value = [{"translation_text": "Audio de prueba"}]
        
        mock_response = MagicMock()
        mock_response.content = b"concurrent_tts_audio"
        mock_openai_tts.return_value.audio.speech.create.return_value = mock_response
        
        # Create multiple connection states
        connections = []
        for i in range(3):
            conn = ConnectionState(
                websocket=mock_websocket,
                user=mock_user,
                call_id=f"concurrent-call-{i}",
                state=CallState.ACTIVE,
            )
            connections.append(conn)
        
        # Add audio to each connection
        for i, conn in enumerate(connections):
            audio_sample = speech_audio if i % 2 == 0 else silence_audio
            raw_audio = audio_sample[44:][:1024]
            await conn.inbound_queue.put(raw_audio)
        
        # Process all connections concurrently
        processor = S2STProcessor()
        tasks = [processor.process_audio_chunk(conn) for conn in connections]
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Verify all connections were processed
        for conn in connections:
            assert conn.total_audio_received > 0


class TestPerformanceWithRealAudio:
    """Test performance characteristics with real audio samples."""

    @pytest.mark.asyncio
    async def test_audio_processing_latency(
        self,
        speech_audio,
        connection_state,
        mock_vad,
        mock_whisper,
        mock_translator,
        mock_openai_tts
    ):
        """Test audio processing latency with real audio."""
        import time
        
        # Setup fast mocks
        mock_vad.is_speech.return_value = True
        mock_whisper.transcribe.return_value = (
            [{"text": "Quick test", "start": 0.0, "end": 1.0}],
            {"language": "en", "language_probability": 0.95}
        )
        mock_translator.return_value = [{"translation_text": "Prueba rápida"}]
        
        mock_response = MagicMock()
        mock_response.content = b"fast_tts_audio"
        mock_openai_tts.return_value.audio.speech.create.return_value = mock_response
        
        processor = S2STProcessor()
        
        # Add audio chunk
        raw_audio = speech_audio[44:][:1024]
        await connection_state.inbound_queue.put(raw_audio)
        
        # Measure processing time
        start_time = time.time()
        await processor.process_audio_chunk(connection_state)
        processing_time = time.time() - start_time
        
        # Processing should be reasonably fast (under 1 second for mocked components)
        assert processing_time < 1.0
        
        # Verify processing occurred
        assert connection_state.total_audio_received > 0

    def test_memory_usage_with_large_audio(self, speech_audio):
        """Test memory usage with larger audio samples."""
        import sys
        
        # Get initial memory usage
        initial_size = sys.getsizeof(speech_audio)
        
        # Process audio in chunks (simulating streaming)
        raw_audio = speech_audio[44:]
        chunk_size = 1024
        chunks = []
        
        for i in range(0, len(raw_audio), chunk_size):
            chunk = raw_audio[i:i + chunk_size]
            chunks.append(chunk)
        
        # Memory usage should be reasonable
        total_chunk_size = sum(sys.getsizeof(chunk) for chunk in chunks)
        
        # Chunked processing shouldn't use significantly more memory
        assert total_chunk_size < initial_size * 2
