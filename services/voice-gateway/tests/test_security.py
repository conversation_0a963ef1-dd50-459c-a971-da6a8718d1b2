import pytest
from unittest.mock import patch, MagicMock
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from uuid import uuid4

from cortexacommon.security.auth import TokenD<PERSON>, create_access_token
from cortexacommon.config import SecuritySettings
from src.core.security import (
    get_current_user_ws,
    verify_user_permissions,
    WebSocketConnectionManager,
    connection_manager,
)


class TestGetCurrentUserWS:
    """Test suite for WebSocket JWT authentication."""

    @pytest.mark.asyncio
    async def test_get_current_user_ws_success(self, mock_user):
        """Test successful WebSocket authentication with valid token."""
        test_token = "valid.jwt.token"
        
        with patch("src.core.security.verify_token") as mock_verify:
            mock_verify.return_value = mock_user
            
            result = await get_current_user_ws(token=test_token)
            
            assert result == mock_user
            mock_verify.assert_called_once()
            # Verify the token verification was called with correct parameters
            args, kwargs = mock_verify.call_args
            assert args[0] == test_token
            assert isinstance(args[1], SecuritySettings)
            assert kwargs.get("token_type") == "access"

    @pytest.mark.asyncio
    async def test_get_current_user_ws_missing_token(self):
        """Test WebSocket authentication fails when token is missing."""
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_ws(token=None)
        
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Access token is required" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_current_user_ws_invalid_token(self):
        """Test WebSocket authentication fails with invalid token."""
        test_token = "invalid.jwt.token"
        
        with patch("src.core.security.verify_token") as mock_verify:
            mock_verify.side_effect = Exception("Invalid token")
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user_ws(token=test_token)
            
            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid or expired access token" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_current_user_ws_expired_token(self):
        """Test WebSocket authentication fails with expired token."""
        test_token = "expired.jwt.token"
        
        with patch("src.core.security.verify_token") as mock_verify:
            mock_verify.side_effect = Exception("Token expired")
            
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user_ws(token=test_token)
            
            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid or expired access token" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_get_current_user_ws_empty_token(self):
        """Test WebSocket authentication fails with empty token."""
        with pytest.raises(HTTPException) as exc_info:
            await get_current_user_ws(token="")
        
        # Empty string should be treated as missing token
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED


class TestVerifyUserPermissions:
    """Test suite for user permission verification."""

    @pytest.mark.asyncio
    async def test_verify_user_permissions_no_requirements(self, mock_user):
        """Test permission verification with no specific requirements."""
        result = await verify_user_permissions(mock_user, required_permissions=None)
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_user_permissions_admin_role(self):
        """Test permission verification for admin user."""
        import uuid
        admin_user = TokenData(
            user_id=str(uuid.uuid4()),
            username="admin",
            email="<EMAIL>",
            roles=["ADMIN"],
            permissions=["all"],
            exp=9999999999,
        )
        admin_user.role = "ADMIN"  # Set role attribute

        result = await verify_user_permissions(admin_user, required_permissions=["voice:translate"])
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_user_permissions_manager_role(self):
        """Test permission verification for manager user."""
        import uuid
        manager_user = TokenData(
            user_id=str(uuid.uuid4()),
            username="manager",
            email="<EMAIL>",
            roles=["MANAGER"],
            permissions=["voice:translate"],
            exp=9999999999,
        )
        manager_user.role = "MANAGER"  # Set role attribute

        result = await verify_user_permissions(manager_user, required_permissions=["voice:translate"])
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_user_permissions_operator_role(self):
        """Test permission verification for operator user."""
        import uuid
        operator_user = TokenData(
            user_id=str(uuid.uuid4()),
            username="operator",
            email="<EMAIL>",
            roles=["OPERATOR"],
            permissions=["voice:translate"],
            exp=9999999999,
        )
        operator_user.role = "OPERATOR"  # Set role attribute

        result = await verify_user_permissions(operator_user, required_permissions=["voice:translate"])
        assert result is True

    @pytest.mark.asyncio
    async def test_verify_user_permissions_insufficient_role(self):
        """Test permission verification fails for insufficient role."""
        import uuid
        user_user = TokenData(
            user_id=str(uuid.uuid4()),
            username="user",
            email="<EMAIL>",
            roles=["USER"],
            permissions=["basic"],
            exp=9999999999,
        )
        user_user.role = "USER"  # Set role attribute

        result = await verify_user_permissions(user_user, required_permissions=["voice:translate"])
        assert result is False


class TestWebSocketConnectionManager:
    """Test suite for WebSocket connection management."""

    def test_connection_manager_initialization(self):
        """Test connection manager initializes correctly."""
        manager = WebSocketConnectionManager()
        assert isinstance(manager.active_connections, dict)
        assert len(manager.active_connections) == 0

    def test_add_connection(self, mock_user):
        """Test adding a WebSocket connection."""
        manager = WebSocketConnectionManager()
        mock_websocket = MagicMock()
        call_id = "test-call-123"
        
        manager.add_connection(call_id, mock_websocket, mock_user)
        
        assert call_id in manager.active_connections
        connection = manager.active_connections[call_id]
        assert connection["websocket"] == mock_websocket
        assert connection["user"] == mock_user
        assert connection["authenticated"] is True

    def test_remove_connection(self, mock_user):
        """Test removing a WebSocket connection."""
        manager = WebSocketConnectionManager()
        mock_websocket = MagicMock()
        call_id = "test-call-123"
        
        # Add connection first
        manager.add_connection(call_id, mock_websocket, mock_user)
        assert call_id in manager.active_connections
        
        # Remove connection
        manager.remove_connection(call_id)
        assert call_id not in manager.active_connections

    def test_remove_nonexistent_connection(self):
        """Test removing a connection that doesn't exist."""
        manager = WebSocketConnectionManager()
        
        # Should not raise an error
        manager.remove_connection("nonexistent-call")
        assert len(manager.active_connections) == 0

    def test_get_connection(self, mock_user):
        """Test getting connection information."""
        manager = WebSocketConnectionManager()
        mock_websocket = MagicMock()
        call_id = "test-call-123"
        
        # Test getting nonexistent connection
        result = manager.get_connection(call_id)
        assert result is None
        
        # Add connection and test getting it
        manager.add_connection(call_id, mock_websocket, mock_user)
        result = manager.get_connection(call_id)
        
        assert result is not None
        assert result["websocket"] == mock_websocket
        assert result["user"] == mock_user
        assert result["authenticated"] is True

    def test_is_authenticated(self, mock_user):
        """Test checking if connection is authenticated."""
        manager = WebSocketConnectionManager()
        mock_websocket = MagicMock()
        call_id = "test-call-123"
        
        # Test unauthenticated (nonexistent) connection
        assert manager.is_authenticated(call_id) is False
        
        # Add authenticated connection
        manager.add_connection(call_id, mock_websocket, mock_user)
        assert manager.is_authenticated(call_id) is True

    def test_get_active_connection_count(self, mock_user):
        """Test getting active connection count."""
        manager = WebSocketConnectionManager()
        mock_websocket = MagicMock()
        
        # Initially no connections
        assert manager.get_active_connection_count() == 0
        
        # Add connections
        manager.add_connection("call-1", mock_websocket, mock_user)
        assert manager.get_active_connection_count() == 1
        
        manager.add_connection("call-2", mock_websocket, mock_user)
        assert manager.get_active_connection_count() == 2
        
        # Remove connection
        manager.remove_connection("call-1")
        assert manager.get_active_connection_count() == 1

    def test_multiple_connections(self, mock_user):
        """Test managing multiple WebSocket connections."""
        manager = WebSocketConnectionManager()
        mock_websocket1 = MagicMock()
        mock_websocket2 = MagicMock()
        
        call_id1 = "call-1"
        call_id2 = "call-2"
        
        # Add multiple connections
        manager.add_connection(call_id1, mock_websocket1, mock_user)
        manager.add_connection(call_id2, mock_websocket2, mock_user)
        
        assert manager.get_active_connection_count() == 2
        assert manager.is_authenticated(call_id1) is True
        assert manager.is_authenticated(call_id2) is True
        
        # Verify each connection has correct data
        conn1 = manager.get_connection(call_id1)
        conn2 = manager.get_connection(call_id2)
        
        assert conn1["websocket"] == mock_websocket1
        assert conn2["websocket"] == mock_websocket2

    def test_global_connection_manager_instance(self):
        """Test that global connection manager instance exists."""
        assert connection_manager is not None
        assert isinstance(connection_manager, WebSocketConnectionManager)


class TestJWTIntegration:
    """Test suite for JWT token integration between services."""

    @pytest.mark.asyncio
    async def test_jwt_token_verification_with_same_secret(self):
        """Test that JWT tokens can be created and verified with the same secret key."""
        # Create a security settings instance (this will use the .env file)
        security_settings = SecuritySettings()

        # Create test user data
        user_id = str(uuid4())
        test_data = {
            "sub": user_id,
            "role": "OPERATOR"
        }

        # Create a JWT token (simulating what authenticator service does)
        token = create_access_token(data=test_data, security_settings=security_settings)

        # Verify the token can be decoded (simulating what voice-gateway does)
        result = await get_current_user_ws(token=token)

        # Verify the decoded data matches
        assert str(result.user_id) == user_id
        assert result.role == "OPERATOR"

    @pytest.mark.asyncio
    async def test_jwt_token_verification_with_different_secret_fails(self):
        """Test that JWT tokens fail verification when using different secret keys."""
        # Create token with one secret key
        security_settings_1 = SecuritySettings()
        security_settings_1.secret_key = "secret-key-1"

        user_id = str(uuid4())
        test_data = {
            "sub": user_id,
            "role": "OPERATOR"
        }

        token = create_access_token(data=test_data, security_settings=security_settings_1)

        # Try to verify with different secret key (patch the SecuritySettings)
        with patch("src.core.security.SecuritySettings") as mock_settings_class:
            mock_settings = MagicMock()
            mock_settings.secret_key = "secret-key-2"  # Different secret key
            mock_settings.algorithm = "HS256"
            mock_settings_class.return_value = mock_settings

            # This should fail with invalid signature
            with pytest.raises(HTTPException) as exc_info:
                await get_current_user_ws(token=token)

            assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
            assert "Invalid or expired access token" in exc_info.value.detail
