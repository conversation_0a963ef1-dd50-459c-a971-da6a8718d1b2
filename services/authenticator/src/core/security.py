from datetime import timed<PERSON><PERSON>
from typing import Any, Dict, Optional

from cortexacommon.security import (
    create_access_token as _create_access_token,
    create_refresh_token as _create_refresh_token,
    get_password_hash,
    verify_password,
)

from .config import settings


def create_access_token(
    data: Dict[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create a JWT access token with service-specific settings."""
    return _create_access_token(
        data=data,
        security_settings=settings.security,
        expires_delta=expires_delta
    )


def create_refresh_token(
    data: Dict[str, Any], 
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create a JWT refresh token with service-specific settings."""
    return _create_refresh_token(
        data=data,
        security_settings=settings.security,
        expires_delta=expires_delta
    )


def create_tokens_for_user(user_id: str, role: str) -> Dict[str, str]:
    """Create both access and refresh tokens for a user."""
    token_data = {
        "sub": str(user_id),
        "role": role,
    }
    
    access_token = create_access_token(data=token_data)
    refresh_token = create_refresh_token(data=token_data)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }


# Re-export password utilities for convenience
__all__ = [
    "create_access_token",
    "create_refresh_token", 
    "create_tokens_for_user",
    "get_password_hash",
    "verify_password",
]
