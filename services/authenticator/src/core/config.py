from pydantic import Field

from cortexacommon.config import BaseServiceSettings


class AuthServiceSettings(BaseServiceSettings):
    """Settings for the authentication service."""
    
    service_name: str = Field(
        default="cortexa-auth-service",
        description="Name of the authentication service"
    )
    
    port: int = Field(
        default=8001,
        description="Port for the authentication service"
    )


# Global settings instance
settings = AuthServiceSettings()
