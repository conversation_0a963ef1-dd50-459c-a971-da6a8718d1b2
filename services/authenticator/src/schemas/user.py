from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import EmailStr, Field, field_validator

from cortexacommon.schemas import BaseSchema
from src.core.roles import UserRole, get_valid_roles


class UserBase(BaseSchema):
    """Base user schema with common fields."""
    
    username: str = Field(
        min_length=3,
        max_length=50,
        description="Unique username"
    )
    email: EmailStr = Field(description="User email address")
    role: str = Field(
        default=UserRole.OPERATOR.value,
        description=f"User role ({', '.join(get_valid_roles())})"
    )
    is_active: bool = Field(default=True, description="Whether the user is active")
    
    @field_validator("role")
    @classmethod
    def validate_role(cls, v: str) -> str:
        """Validate that role is one of the allowed values."""
        valid_roles = get_valid_roles()
        if v not in valid_roles:
            raise ValueError(f"Role must be one of: {', '.join(valid_roles)}")
        return v
    
    @field_validator("username")
    @classmethod
    def validate_username(cls, v: str) -> str:
        """Validate username format."""
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Username can only contain letters, numbers, hyphens, and underscores")
        return v.lower()


class UserCreate(UserBase):
    """Schema for creating a new user."""
    
    password: str = Field(
        min_length=8,
        max_length=128,
        description="User password"
    )
    
    @field_validator("password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, and one digit"
            )
        
        return v


class UserUpdate(BaseSchema):
    """Schema for updating a user."""
    
    username: Optional[str] = Field(
        None,
        min_length=3,
        max_length=50,
        description="Unique username"
    )
    email: Optional[EmailStr] = Field(None, description="User email address")
    role: Optional[str] = Field(
        None,
        description=f"User role ({', '.join(get_valid_roles())})"
    )
    is_active: Optional[bool] = Field(None, description="Whether the user is active")
    
    @field_validator("role")
    @classmethod
    def validate_role(cls, v: Optional[str]) -> Optional[str]:
        """Validate that role is one of the allowed values."""
        if v is None:
            return v

        valid_roles = get_valid_roles()
        if v not in valid_roles:
            raise ValueError(f"Role must be one of: {', '.join(valid_roles)}")
        return v
    
    @field_validator("username")
    @classmethod
    def validate_username(cls, v: Optional[str]) -> Optional[str]:
        """Validate username format."""
        if v is None:
            return v
        
        if not v.replace("_", "").replace("-", "").isalnum():
            raise ValueError("Username can only contain letters, numbers, hyphens, and underscores")
        return v.lower()


class UserResponse(UserBase):
    """Schema for user response."""
    
    id: UUID = Field(description="User ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")


class UserPasswordUpdate(BaseSchema):
    """Schema for updating user password."""
    
    current_password: str = Field(description="Current password")
    new_password: str = Field(
        min_length=8,
        max_length=128,
        description="New password"
    )
    
    @field_validator("new_password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, and one digit"
            )
        
        return v


# Authentication schemas
class LoginRequest(BaseSchema):
    """Schema for login request."""
    
    username: str = Field(description="Username")
    password: str = Field(description="Password")


class TokenResponse(BaseSchema):
    """Schema for token response."""
    
    access_token: str = Field(description="JWT access token")
    refresh_token: str = Field(description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")


class RefreshTokenRequest(BaseSchema):
    """Schema for refresh token request."""
    
    refresh_token: str = Field(description="Refresh token")
