from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from cortexacommon.db import init_db
from cortexacommon.monitoring import setup_monitoring
from cortexacommon.logging import setup_service_logging, get_logger

from src.api.v1.router import api_router
from src.core.config import settings

# Setup common logging and monitoring
setup_service_logging(service_name=settings.service_name, level="INFO")
setup_monitoring(service_name=settings.service_name, app=None)
logger = get_logger(__name__)

from src.core.config import settings


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    # Startup
    init_db(settings.database)
    yield
    # Shutdown - cleanup if needed


# Create FastAPI application
app = FastAPI(
    title="Cortexa Authentication Service",
    description="Authentication and user management service for the Cortexa platform",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API router
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root() -> dict[str, str]:
    """Root endpoint."""
    return {"message": "Cortexa Authentication Service", "version": "0.1.0"}


@app.get("/health")
async def health_check() -> dict[str, str]:
    """Health check endpoint."""
    return {"status": "healthy", "service": "cortexa-auth-service"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
    )
