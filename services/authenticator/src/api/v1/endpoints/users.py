"""User management endpoints."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from cortexacommon.db import get_async_session
from cortexacommon.schemas import <PERSON><PERSON><PERSON>po<PERSON>, PaginatedResponse
from cortexacommon.security import <PERSON><PERSON><PERSON><PERSON>, get_current_active_user
from cortexacommon.security.auth import TokenData
from src.core.roles import UserRole, Permission, get_roles_with_permission
from src.crud import UserRepository
from src.schemas import (
    UserCreate,
    UserPasswordUpdate,
    UserResponse,
    UserUpdate,
)

router = APIRouter()

# Role-based access control dependencies
admin_required = Depends(RoleChecker(allowed_roles=[UserRole.ADMIN.value]))
manager_or_admin_required = Depends(RoleChecker(
    allowed_roles=list(get_roles_with_permission(Permission.LIST_USERS))
))


@router.post("/", response_model=SuccessResponse[UserResponse], dependencies=[admin_required])
async def create_user(
    user_data: UserCreate,
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[UserResponse]:
    """
    Create a new user.
    
    Only ADMIN users can create new users.
    """
    user_repo = UserRepository(session)
    
    # Check if username already exists
    existing_user = await user_repo.get_user_by_username(user_data.username)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Check if email already exists
    existing_email = await user_repo.get_user_by_email(user_data.email)
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create the user
    user = await user_repo.create_user(user_data)
    
    return SuccessResponse(
        message="User created successfully",
        data=UserResponse.model_validate(user)
    )


@router.get("/", response_model=PaginatedResponse[UserResponse], dependencies=[manager_or_admin_required])
async def list_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of users to return"),
    role: str = Query(None, description="Filter by role"),
    is_active: bool = Query(None, description="Filter by active status"),
    session: AsyncSession = Depends(get_async_session),
) -> PaginatedResponse[UserResponse]:
    """
    List users with pagination and filtering.
    
    ADMIN and MANAGER users can list users.
    """
    user_repo = UserRepository(session)
    
    # Build filters
    filters = {}
    if role:
        filters["role"] = role
    if is_active is not None:
        filters["is_active"] = is_active
    
    # Get users and count
    users = await user_repo.get_multi(skip=skip, limit=limit, **filters)
    total_count = await user_repo.count(**filters)
    
    # Convert to response models
    user_responses = [UserResponse.model_validate(user) for user in users]
    
    return PaginatedResponse(
        message="Users retrieved successfully",
        data=user_responses,
        pagination={
            "skip": skip,
            "limit": limit,
            "total": total_count,
            "has_more": skip + limit < total_count
        }
    )


@router.get("/me", response_model=SuccessResponse[UserResponse])
async def get_current_user_info(
    current_user: TokenData = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[UserResponse]:
    """
    Get current user information.
    
    Any authenticated user can get their own information.
    """
    user_repo = UserRepository(session)
    user = await user_repo.get(current_user.user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return SuccessResponse(
        message="User information retrieved successfully",
        data=UserResponse.model_validate(user)
    )


@router.get("/{user_id}", response_model=SuccessResponse[UserResponse], dependencies=[manager_or_admin_required])
async def get_user(
    user_id: UUID,
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[UserResponse]:
    """
    Get a specific user by ID.
    
    ADMIN and MANAGER users can get user information.
    """
    user_repo = UserRepository(session)
    user = await user_repo.get(user_id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return SuccessResponse(
        message="User retrieved successfully",
        data=UserResponse.model_validate(user)
    )


@router.put("/{user_id}", response_model=SuccessResponse[UserResponse], dependencies=[admin_required])
async def update_user(
    user_id: UUID,
    user_update: UserUpdate,
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[UserResponse]:
    """
    Update a user.
    
    Only ADMIN users can update user information.
    """
    user_repo = UserRepository(session)
    
    # Check if user exists
    existing_user = await user_repo.get(user_id)
    if not existing_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Check for username conflicts if username is being updated
    if user_update.username and user_update.username != existing_user.username:
        username_conflict = await user_repo.get_user_by_username(user_update.username)
        if username_conflict:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
    
    # Check for email conflicts if email is being updated
    if user_update.email and user_update.email != existing_user.email:
        email_conflict = await user_repo.get_user_by_email(user_update.email)
        if email_conflict:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already taken"
            )
    
    # Update the user
    updated_user = await user_repo.update(user_id, user_update)
    
    return SuccessResponse(
        message="User updated successfully",
        data=UserResponse.model_validate(updated_user)
    )


@router.put("/me/password", response_model=SuccessResponse[None])
async def update_current_user_password(
    password_update: UserPasswordUpdate,
    current_user: TokenData = Depends(get_current_active_user),
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[None]:
    """
    Update current user's password.

    Any authenticated user can update their own password.
    """
    user_repo = UserRepository(session)

    # Get current user
    user = await user_repo.get(current_user.user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Verify current password
    from src.core.security import verify_password
    if not verify_password(password_update.current_password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )

    # Update password
    await user_repo.update_password(user.id, password_update.new_password)

    return SuccessResponse(
        message="Password updated successfully",
        data=None
    )


@router.delete("/{user_id}", response_model=SuccessResponse[None], dependencies=[admin_required])
async def deactivate_user(
    user_id: UUID,
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[None]:
    """
    Deactivate a user account.

    Only ADMIN users can deactivate user accounts.
    Note: This doesn't actually delete the user, just deactivates them.
    """
    user_repo = UserRepository(session)

    user = await user_repo.deactivate_user(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return SuccessResponse(
        message="User deactivated successfully",
        data=None
    )


@router.post("/{user_id}/activate", response_model=SuccessResponse[UserResponse], dependencies=[admin_required])
async def activate_user(
    user_id: UUID,
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[UserResponse]:
    """
    Activate a user account.

    Only ADMIN users can activate user accounts.
    """
    user_repo = UserRepository(session)

    user = await user_repo.activate_user(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    return SuccessResponse(
        message="User activated successfully",
        data=UserResponse.model_validate(user)
    )
