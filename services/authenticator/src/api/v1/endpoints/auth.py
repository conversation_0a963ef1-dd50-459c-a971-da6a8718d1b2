"""Authentication endpoints."""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession

from cortexacommon.db import get_async_session
from cortexacommon.security import verify_token
from cortexacommon.schemas import SuccessResponse
from src.core.config import settings
from src.core.security import create_tokens_for_user
from src.crud import UserRepository
from src.schemas import LoginRequest, RefreshTokenRequest, TokenResponse

router = APIRouter()


@router.post("/token", response_model=SuccessResponse[TokenResponse])
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[TokenResponse]:
    """
    OAuth2 compatible token login, get an access token for future requests.
    
    This endpoint accepts form data with username and password fields
    and returns JWT access and refresh tokens.
    """
    user_repo = UserRepository(session)
    
    # Authenticate user
    user = await user_repo.authenticate_user(form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create tokens
    tokens = create_tokens_for_user(str(user.id), user.role)
    
    return SuccessResponse(
        message="Login successful",
        data=TokenResponse(**tokens)
    )


@router.post("/login", response_model=SuccessResponse[TokenResponse])
async def login(
    login_data: LoginRequest,
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[TokenResponse]:
    """
    Alternative login endpoint that accepts JSON data.
    
    This endpoint accepts JSON with username and password fields
    and returns JWT access and refresh tokens.
    """
    user_repo = UserRepository(session)
    
    # Authenticate user
    user = await user_repo.authenticate_user(login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
        )
    
    # Create tokens
    tokens = create_tokens_for_user(str(user.id), user.role)
    
    return SuccessResponse(
        message="Login successful",
        data=TokenResponse(**tokens)
    )


@router.post("/refresh", response_model=SuccessResponse[TokenResponse])
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[TokenResponse]:
    """
    Refresh an access token using a refresh token.
    
    This endpoint accepts a refresh token and returns new access and refresh tokens.
    """
    try:
        # Verify the refresh token
        token_data = verify_token(
            refresh_data.refresh_token, 
            settings.security, 
            token_type="refresh"
        )
    except HTTPException:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
        )
    
    # Get user to verify they still exist and are active
    user_repo = UserRepository(session)
    user = await user_repo.get(token_data.user_id)
    
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive",
        )
    
    # Create new tokens
    tokens = create_tokens_for_user(str(user.id), user.role)
    
    return SuccessResponse(
        message="Token refreshed successfully",
        data=TokenResponse(**tokens)
    )


@router.post("/logout", response_model=SuccessResponse[None])
async def logout() -> SuccessResponse[None]:
    """
    Logout endpoint.
    
    Since we're using stateless JWT tokens, this endpoint mainly serves
    as a signal to the client to discard their tokens. In a production
    system, you might want to implement token blacklisting.
    """
    return SuccessResponse(
        message="Logged out successfully",
        data=None
    )
