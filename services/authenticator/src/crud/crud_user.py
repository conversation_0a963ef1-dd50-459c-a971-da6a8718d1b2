"""User repository for database operations."""

from typing import Optional
from uuid import <PERSON><PERSON><PERSON>

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from cortexacommon.db import BaseRepository
from src.models.user import User
from src.schemas.user import User<PERSON><PERSON>, UserUpdate


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """Repository for user database operations."""
    
    def __init__(self, session: AsyncSession):
        """Initialize the user repository."""
        super().__init__(User, session)
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get a user by username."""
        result = await self.session.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by email address."""
        result = await self.session.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def get_active_user_by_username(self, username: str) -> Optional[User]:
        """Get an active user by username."""
        result = await self.session.execute(
            select(User).where(
                User.username == username,
                User.is_active == True  # noqa: E712
            )
        )
        return result.scalar_one_or_none()
    
    async def get_active_user_by_email(self, email: str) -> Optional[User]:
        """Get an active user by email address."""
        result = await self.session.execute(
            select(User).where(
                User.email == email,
                User.is_active == True  # noqa: E712
            )
        )
        return result.scalar_one_or_none()
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate a user by username and password."""
        from src.core.security import verify_password
        
        user = await self.get_active_user_by_username(username)
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        return user
    
    async def create_user(self, user_data: UserCreate) -> User:
        """Create a new user with hashed password."""
        from src.core.security import get_password_hash
        
        # Hash the password before storing
        hashed_password = get_password_hash(user_data.password)
        
        # Create user data dict without the plain password
        user_dict = user_data.model_dump(exclude={"password"})
        user_dict["hashed_password"] = hashed_password
        
        # Create the user
        db_user = User(**user_dict)
        self.session.add(db_user)
        await self.session.commit()
        await self.session.refresh(db_user)
        
        return db_user
    
    async def update_password(self, user_id: UUID, new_password: str) -> Optional[User]:
        """Update a user's password."""
        from src.core.security import get_password_hash
        
        user = await self.get(user_id)
        if not user:
            return None
        
        user.hashed_password = get_password_hash(new_password)
        await self.session.commit()
        await self.session.refresh(user)
        
        return user
    
    async def deactivate_user(self, user_id: UUID) -> Optional[User]:
        """Deactivate a user account."""
        user = await self.get(user_id)
        if not user:
            return None
        
        user.is_active = False
        await self.session.commit()
        await self.session.refresh(user)
        
        return user
    
    async def activate_user(self, user_id: UUID) -> Optional[User]:
        """Activate a user account."""
        user = await self.get(user_id)
        if not user:
            return None
        
        user.is_active = True
        await self.session.commit()
        await self.session.refresh(user)
        
        return user
