from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import Column, DateTime, func
from sqlmodel import Field, SQLModel

from src.core.roles import UserRole


class User(SQLModel, table=True):
    """User model for authentication and authorization."""
    
    __tablename__ = "users"
    
    id: Optional[UUID] = Field(
        default_factory=uuid4,
        primary_key=True,
        description="Unique user identifier"
    )
    
    username: str = Field(
        max_length=50,
        unique=True,
        index=True,
        description="Unique username for login"
    )
    
    email: str = Field(
        max_length=255,
        unique=True,
        index=True,
        description="User email address"
    )
    
    hashed_password: str = Field(
        max_length=255,
        description="Hashed password"
    )
    
    role: str = Field(
        max_length=50,
        default=UserRole.OPERATOR.value,
        description="User role (ADMIN, MANAGER, OPERATOR, TRANSLATOR)"
    )
    
    is_active: bool = Field(
        default=True,
        description="Whether the user account is active"
    )
    
    created_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(timezone=True), server_default=func.now()),
        description="Account creation timestamp"
    )
    
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime(timezone=True), 
            server_default=func.now(),
            onupdate=func.now()
        ),
        description="Last update timestamp"
    )
    
    def __repr__(self) -> str:
        """String representation of the user."""
        return f"<User(id={self.id}, username={self.username}, role={self.role})>"
