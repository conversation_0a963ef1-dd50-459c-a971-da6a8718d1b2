from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, AsyncMock
from uuid import uuid4

from src.models.user import User


class TestUserEndpoints:
    """Test user management endpoints."""
    
    @patch("src.api.v1.endpoints.users.UserRepository")
    def test_get_current_user_info(self, mock_repo_class, authenticated_client: TestClient, sample_user_data, mock_admin_user):
        """Test getting current user information."""
        # Use the admin user ID from the fixture
        user_id = mock_admin_user.user_id

        # Mock user
        mock_user = User(
            id=user_id,
            username=sample_user_data["username"],
            email=sample_user_data["email"],
            hashed_password="hashed_password",
            role=sample_user_data["role"],
            is_active=True
        )

        # Mock repository
        mock_repo = AsyncMock()
        mock_repo.get.return_value = mock_user
        mock_repo_class.return_value = mock_repo

        # Test get current user
        response = authenticated_client.get("/api/v1/users/me")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "User information retrieved successfully"
        assert data["data"]["username"] == sample_user_data["username"]
        assert data["data"]["email"] == sample_user_data["email"]
    
    @patch("src.api.v1.endpoints.users.UserRepository")
    def test_create_user_success(self, mock_repo_class, authenticated_client: TestClient, sample_user_data):
        """Test successful user creation."""
        # Mock repository
        mock_repo = AsyncMock()
        mock_repo.get_user_by_username.return_value = None
        mock_repo.get_user_by_email.return_value = None
        
        # Mock created user
        created_user = User(
            id=uuid4(),
            username=sample_user_data["username"],
            email=sample_user_data["email"],
            hashed_password="hashed_password",
            role=sample_user_data["role"],
            is_active=True
        )
        mock_repo.create_user.return_value = created_user
        mock_repo_class.return_value = mock_repo

        # Test create user
        response = authenticated_client.post(
            "/api/v1/users/",
            json=sample_user_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "User created successfully"
        assert data["data"]["username"] == sample_user_data["username"]
    
    @patch("src.api.v1.endpoints.users.UserRepository")
    def test_create_user_duplicate_username(self, mock_repo_class, authenticated_client: TestClient, sample_user_data):
        """Test user creation with duplicate username."""
        # Mock repository
        mock_repo = AsyncMock()
        mock_repo.get_user_by_username.return_value = User(
            id=uuid4(),
            username=sample_user_data["username"],
            email="<EMAIL>",
            hashed_password="hashed_password",
            role="OPERATOR",
            is_active=True
        )
        mock_repo_class.return_value = mock_repo

        # Test create user
        response = authenticated_client.post(
            "/api/v1/users/",
            json=sample_user_data
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["detail"] == "Username already registered"
    
    def test_password_validation(self, authenticated_client: TestClient):
        """Test password validation in user creation."""
        invalid_user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "weak",  # Too weak
            "role": "OPERATOR"
        }

        response = authenticated_client.post(
            "/api/v1/users/",
            json=invalid_user_data
        )

        assert response.status_code == 422  # Validation error
    
    def test_role_validation(self, authenticated_client: TestClient):
        """Test role validation in user creation."""
        invalid_user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password": "ValidPassword123",
            "role": "INVALID_ROLE"  # Invalid role
        }

        response = authenticated_client.post(
            "/api/v1/users/",
            json=invalid_user_data
        )

        assert response.status_code == 422  # Validation error
