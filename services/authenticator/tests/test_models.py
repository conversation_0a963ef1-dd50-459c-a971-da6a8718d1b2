from uuid import uuid4

from src.models.user import User


class TestUserModel:
    """Test User model."""
    
    def test_user_creation(self):
        """Test creating a user instance."""
        user_id = uuid4()
        user = User(
            id=user_id,
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            role="OPERATOR",
            is_active=True
        )
        
        assert user.id == user_id
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.hashed_password == "hashed_password"
        assert user.role == "OPERATOR"
        assert user.is_active is True
    
    def test_user_repr(self):
        """Test user string representation."""
        user_id = uuid4()
        user = User(
            id=user_id,
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password",
            role="OPERATOR",
            is_active=True
        )
        
        expected_repr = f"<User(id={user_id}, username=testuser, role=OPERATOR)>"
        assert repr(user) == expected_repr
    
    def test_user_defaults(self):
        """Test user default values."""
        user = User(
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed_password"
        )
        
        assert user.role == "OPERATOR"  # Default role
        assert user.is_active is True  # Default active status
        assert user.id is not None  # UUID should be generated
