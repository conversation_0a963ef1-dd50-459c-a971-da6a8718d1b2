import asyncio
from typing import Generator
from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from cortexacommon.db import get_async_session
from cortexacommon.security.auth import TokenData
from src.main import app


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client() -> Generator[TestClient, None, None]:
    """Create a test client with mocked database."""
    # Mock the database session dependency
    def mock_get_session():
        return AsyncMock(spec=AsyncSession)

    # Override the dependency
    app.dependency_overrides[get_async_session] = mock_get_session

    client = TestClient(app)

    # Clean up after test
    yield client
    app.dependency_overrides.clear()


@pytest.fixture
async def mock_session() -> AsyncMock:
    """Create a mock database session."""
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def sample_user_data() -> dict:
    """Sample user data for testing."""
    return {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "TestPassword123",
        "role": "OPERATOR",
        "is_active": True
    }


@pytest.fixture
def sample_admin_data() -> dict:
    """Sample admin user data for testing."""
    return {
        "username": "admin",
        "email": "<EMAIL>",
        "password": "AdminPassword123",
        "role": "ADMIN",
        "is_active": True
    }


@pytest.fixture
def mock_admin_user():
    """Mock admin user for testing protected endpoints."""
    from uuid import uuid4
    return TokenData(user_id=uuid4(), role="ADMIN")


@pytest.fixture
def authenticated_client(mock_admin_user) -> Generator[TestClient, None, None]:
    """Create a test client with mocked authentication."""
    from cortexacommon.security import get_current_active_user
    from cortexacommon.security.auth import RoleChecker

    # Mock the database session dependency
    def mock_get_session():
        return AsyncMock(spec=AsyncSession)

    # Mock authentication to return admin user
    def mock_get_current_user():
        return mock_admin_user

    # Mock role checker to always pass
    def mock_role_checker(*args, **kwargs):
        def dependency():
            return None
        return dependency

    # Override dependencies
    app.dependency_overrides[get_async_session] = mock_get_session
    app.dependency_overrides[get_current_active_user] = mock_get_current_user

    # Mock RoleChecker class
    with patch('cortexacommon.security.auth.RoleChecker', side_effect=mock_role_checker):
        client = TestClient(app)
        yield client

    # Clean up after test
    app.dependency_overrides.clear()
