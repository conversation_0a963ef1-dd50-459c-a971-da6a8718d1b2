from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, AsyncMock
from uuid import uuid4

from src.models.user import User


class TestAuthEndpoints:
    """Test authentication endpoints."""
    
    def test_health_check(self, client: TestClient):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "healthy", "service": "cortexa-auth-service"}
    
    def test_root_endpoint(self, client: TestClient):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Cortexa Authentication Service"
        assert data["version"] == "0.1.0"
    
    @patch("src.api.v1.endpoints.auth.UserRepository")
    def test_login_success(self, mock_repo_class, client: TestClient, sample_user_data):
        """Test successful login."""
        # Mock user
        mock_user = User(
            id=uuid4(),
            username=sample_user_data["username"],
            email=sample_user_data["email"],
            hashed_password="hashed_password",
            role=sample_user_data["role"],
            is_active=True
        )
        
        # Mock repository
        mock_repo = AsyncMock()
        mock_repo.authenticate_user.return_value = mock_user
        mock_repo_class.return_value = mock_repo
        
        # Test login
        response = client.post(
            "/api/v1/auth/login",
            json={
                "username": sample_user_data["username"],
                "password": sample_user_data["password"]
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Login successful"
        assert "access_token" in data["data"]
        assert "refresh_token" in data["data"]
        assert data["data"]["token_type"] == "bearer"
    
    @patch("src.api.v1.endpoints.auth.UserRepository")
    def test_login_invalid_credentials(self, mock_repo_class, client: TestClient):
        """Test login with invalid credentials."""
        # Mock repository
        mock_repo = AsyncMock()
        mock_repo.authenticate_user.return_value = None
        mock_repo_class.return_value = mock_repo
        
        # Test login
        response = client.post(
            "/api/v1/auth/login",
            json={
                "username": "invalid",
                "password": "invalid"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data["detail"] == "Incorrect username or password"
    
    @patch("src.api.v1.endpoints.auth.UserRepository")
    def test_oauth2_token_login(self, mock_repo_class, client: TestClient, sample_user_data):
        """Test OAuth2 token endpoint."""
        # Mock user
        mock_user = User(
            id=uuid4(),
            username=sample_user_data["username"],
            email=sample_user_data["email"],
            hashed_password="hashed_password",
            role=sample_user_data["role"],
            is_active=True
        )
        
        # Mock repository
        mock_repo = AsyncMock()
        mock_repo.authenticate_user.return_value = mock_user
        mock_repo_class.return_value = mock_repo
        
        # Test OAuth2 token endpoint
        response = client.post(
            "/api/v1/auth/token",
            data={
                "username": sample_user_data["username"],
                "password": sample_user_data["password"]
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Login successful"
        assert "access_token" in data["data"]
        assert "refresh_token" in data["data"]
    
    def test_logout(self, client: TestClient):
        """Test logout endpoint."""
        response = client.post("/api/v1/auth/logout")
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Logged out successfully"
