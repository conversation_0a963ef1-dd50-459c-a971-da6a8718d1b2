import logging
from tenacity import after_log, before_log, retry, stop_after_attempt, wait_fixed
import psycopg
from cortexacommon.config import DatabaseSettings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

max_tries = 60 * 5  # 5 minutes
wait_seconds = 1


def _build_sync_dsn(db: DatabaseSettings) -> str:
    # psycopg expects a postgresql:// DSN (without SQLAlchemy driver suffix)
    return f"postgresql://{db.user}:{db.password}@{db.host}:{db.port}/{db.name}"


@retry(
    stop=stop_after_attempt(max_tries),
    wait=wait_fixed(wait_seconds),
    before=before_log(logger, logging.INFO),
    after=after_log(logger, logging.WARN),
)
def wait_for_db() -> None:
    db = DatabaseSettings()
    dsn = _build_sync_dsn(db)
    try:
        logger.info("Attempting to connect to DB %s:%s/%s", db.host, db.port, db.name)
        with psycopg.connect(dsn) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT 1;")
                cur.fetchone()
    except Exception as e:
        logger.error("Database not ready: %s", e)
        raise


def main() -> None:
    logger.info("Waiting for database to be ready...")
    wait_for_db()
    logger.info("Database is ready.")


if __name__ == "__main__":
    main()