import asyncio
import logging
import os
import sys
from pathlib import Path

# Ensure project root is on sys.path so 'src' is importable when running from scripts/
ROOT = Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from cortexacommon.db import init_db, get_async_session
from src.core.config import settings
from src.core.roles import UserRole
from src.crud.crud_user import UserRepository
from src.schemas.user import UserCreate

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_initial_admin() -> None:
    """Create a default admin user if none exists."""
    # Credentials can be overridden via env vars
    username = os.getenv("ADMIN_USERNAME", "admin")
    email = os.getenv("ADMIN_EMAIL", "<EMAIL>")
    password = os.getenv("ADMIN_PASSWORD", "Admin123!")

    # Acquire an async DB session from the common layer
    async for session in get_async_session():
        repo = UserRepository(session)

        # Check if an admin user already exists by username or email
        existing = await repo.get_user_by_username(username)
        if not existing:
            existing = await repo.get_user_by_email(email)

        if existing:
            logger.info("Admin user already exists: %s (%s)", existing.username, existing.email)
            break

        # Create the admin user
        admin = await repo.create_user(
            UserCreate(
                username=username,
                email=email,
                password=password,
                role=UserRole.ADMIN.value,
                is_active=True,
            )
        )
        logger.info("Created initial admin user: %s (%s)", admin.username, admin.email)
        break


def main() -> None:
    logger.info("Initializing database connection...")
    # Initialize async DB engine/session factory from service settings
    init_db(settings.database)
    logger.info("Seeding initial data (admin user if missing)...")
    asyncio.run(create_initial_admin())
    logger.info("Initial data seeding complete")


if __name__ == "__main__":
    main()
