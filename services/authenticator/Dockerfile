FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Set work directory for the service
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Configure Poetry
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

# Copy Poetry files for the auth service
COPY services/authenticator/pyproject.toml services/authenticator/poetry.lock* ./

# Copy cortexa-common package into a path that matches the relative path in pyproject
COPY packages/cortexa-common /packages/cortexa-common

# Install dependencies (only main)
RUN poetry install --only=main --no-root && rm -rf $POETRY_CACHE_DIR

# Copy application code
COPY services/authenticator/ .

# Expose port (for completeness; not used by preseed container)
EXPOSE 8001

# Default command (service runtime). Preseed container overrides this in compose.
CMD ["poetry", "run", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8001"]
