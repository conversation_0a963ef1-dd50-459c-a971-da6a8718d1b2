# Cortexa Authentication Service

Authentication and user management service for the Cortexa platform.

## Features

- User registration and management
- JWT-based authentication
- Role-based access control (RBAC)
- Password hashing and validation
- Token refresh functionality
- RESTful API endpoints

## Roles

- **ADMIN**: Full system access including user management
- **MANAGER**: Call statistics and advanced features
- **OPERATOR**: Basic call data access
- **TRANSLATOR**: Translation-specific features (planned)

## API Endpoints

### Authentication
- `POST /api/v1/auth/token` - OAuth2 compatible login
- `POST /api/v1/auth/login` - JSON login
- `POST /api/v1/auth/refresh` - Refresh access token
- `POST /api/v1/auth/logout` - Logout

### User Management
- `POST /api/v1/users/` - Create user (ADMIN only)
- `GET /api/v1/users/` - List users (ADMIN/MANAGER)
- `GET /api/v1/users/me` - Get current user info
- `GET /api/v1/users/{user_id}` - Get user by ID (ADMIN/MANAGER)
- `PUT /api/v1/users/{user_id}` - Update user (ADMIN only)
- `PUT /api/v1/users/me/password` - Update own password
- `DELETE /api/v1/users/{user_id}` - Deactivate user (ADMIN only)
- `POST /api/v1/users/{user_id}/activate` - Activate user (ADMIN only)

## Running the Service

```bash
# Install dependencies
poetry install

# Run the service
poetry run uvicorn src.main:app --host 0.0.0.0 --port 8001 --reload

# Or use the main module
poetry run python -m src.main
```

## Environment Variables

See `.env.example` for configuration options.

## Database Migrations

```bash
# Generate migration
poetry run alembic revision --autogenerate -m "Description"

# Apply migrations
poetry run alembic upgrade head
```
