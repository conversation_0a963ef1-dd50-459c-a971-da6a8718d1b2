# Service Configuration
SERVICE_NAME=cortexa-auth-service
DEBUG=false
HOST=0.0.0.0
PORT=8001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=cortexa_user
DB_PASSWORD=cortexa_password
DB_NAME=cortexa
DB_ECHO=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=cortexa-auth-service

# Security Configuration
SECURITY_SECRET_KEY=your-super-secret-key-change-in-production
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30
SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7
