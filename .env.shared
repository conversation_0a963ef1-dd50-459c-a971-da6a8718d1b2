# Shared environment across services when using docker-compose
# Keep secrets centralized here so all services see consistent values

# SECURITY
# IMPORTANT: set this to a strong, consistent value across services.
# This must match the value used by any locally-run processes (e.g., dev server)
SECURITY_SECRET_KEY=dev-shared-secret-please-change
SECURITY_ALGORITHM=HS256
SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES=30
SECURITY_REFRESH_TOKEN_EXPIRE_DAYS=7

