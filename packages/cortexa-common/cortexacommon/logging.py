"""
Enhanced logging configuration using loguru for Cortexa services.

This module provides structured logging with JSON formatting, correlation ID support,
and service-specific context for consistent logging across all microservices.
"""

import os
import sys
import json
from typing import Dict, Any, Optional, Union
from contextvars import ContextVar
from datetime import datetime
from uuid import uuid4

from loguru import logger
from pydantic import BaseModel


# Context variables for request tracing
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)


class LogConfig(BaseModel):
    """Configuration for logging setup."""
    
    service_name: str
    level: str = "INFO"
    format_json: bool = False  # Console uses loguru's nice formatting, JSON only for files
    include_trace: bool = True
    log_file: Optional[str] = None
    max_file_size: str = "100 MB"
    retention: str = "30 days"
    compression: str = "gz"
    colorize: bool = True
    diagnose: bool = False


# Custom formatter functions removed - using loguru's built-in serialization


# Text formatter as format string
def get_text_format() -> str:
    """Get text format string for loguru."""
    return (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )


def setup_logging(config: LogConfig) -> None:
    """
    Set up loguru logging with the specified configuration.

    Args:
        config: Logging configuration
    """
    # Remove default handler
    logger.remove()

    # Determine log level
    log_level = config.level.upper()

    # Configure console handler - always use loguru's nice formatting for console
    logger.add(
        sys.stderr,
        format=get_text_format(),
        level=log_level,
        colorize=config.colorize,  # Always colorize console output
        diagnose=config.diagnose,
        enqueue=True,  # Thread-safe logging
        catch=True,    # Catch exceptions in logging
        serialize=False,  # Never serialize console output
    )

    # Configure file handler if specified
    if config.log_file:
        logger.add(
            config.log_file,
            format="{message}",  # Use simple format with serialization
            level=log_level,
            rotation=config.max_file_size,
            retention=config.retention,
            compression=config.compression,
            enqueue=True,
            catch=True,
            serialize=True,  # Always use JSON for file logs
        )

    # Add service name to all log records
    logger.configure(extra={"service_name": config.service_name})

    # Log the configuration
    logger.info(
        "Logging configured",
        service=config.service_name,
        level=log_level,
        json_format=config.format_json,
        file_logging=config.log_file is not None,
    )


def get_logger(name: str):
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Logger instance
    """
    return logger.bind(logger_name=name)


def set_correlation_id(correlation_id: Optional[str] = None) -> str:
    """
    Set correlation ID for the current context.
    
    Args:
        correlation_id: Correlation ID to set, generates new one if None
        
    Returns:
        The correlation ID that was set
    """
    if correlation_id is None:
        correlation_id = str(uuid4())
    
    correlation_id_var.set(correlation_id)
    return correlation_id


def set_request_id(request_id: Optional[str] = None) -> str:
    """
    Set request ID for the current context.
    
    Args:
        request_id: Request ID to set, generates new one if None
        
    Returns:
        The request ID that was set
    """
    if request_id is None:
        request_id = str(uuid4())
    
    request_id_var.set(request_id)
    return request_id


def set_user_id(user_id: Optional[str]) -> None:
    """
    Set user ID for the current context.
    
    Args:
        user_id: User ID to set
    """
    user_id_var.set(user_id)


def get_correlation_id() -> Optional[str]:
    """Get the current correlation ID."""
    return correlation_id_var.get()


def get_request_id() -> Optional[str]:
    """Get the current request ID."""
    return request_id_var.get()


def get_user_id() -> Optional[str]:
    """Get the current user ID."""
    return user_id_var.get()


def clear_context() -> None:
    """Clear all context variables."""
    correlation_id_var.set(None)
    request_id_var.set(None)
    user_id_var.set(None)


# Convenience function for quick setup
def setup_service_logging(
    service_name: str,
    level: str = "INFO",
    json_format: Optional[bool] = None,
    log_file: Optional[str] = None,
) -> None:
    """
    Quick setup for service logging with sensible defaults.
    
    Args:
        service_name: Name of the service
        level: Log level
        json_format: Use JSON format (auto-detect from environment if None)
        log_file: Optional log file path
    """
    # Auto-detect JSON format based on environment
    # Default to readable console output unless explicitly requested
    if json_format is None:
        json_format = os.getenv("LOG_FORMAT", "").lower() == "json"
    
    config = LogConfig(
        service_name=service_name,
        level=level,
        format_json=json_format,
        log_file=log_file,
        colorize=sys.stderr.isatty(),  # Only colorize if terminal
    )
    
    setup_logging(config)
