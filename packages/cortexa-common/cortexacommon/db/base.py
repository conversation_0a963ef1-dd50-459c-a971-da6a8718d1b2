"""Database base configuration and session management."""

from typing import As<PERSON><PERSON>enerator
from sqlalchemy.ext.asyncio import Async<PERSON>ession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData

from ..config import DatabaseSettings


class Base(DeclarativeBase):
    """Base class for all SQLAlchemy models."""


# Global variables for database session management
async_engine = None
async_session_maker = None


def init_db(database_settings: DatabaseSettings) -> None:
    """Initialize the database engine and session maker."""
    global async_engine, async_session_maker
    
    async_engine = create_async_engine(
        database_settings.url,
        echo=False,  # Set to True for SQL query logging
        future=True,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    async_session_maker = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get an async database session."""
    if async_session_maker is None:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
