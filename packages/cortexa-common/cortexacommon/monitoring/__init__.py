"""
Unified monitoring setup for Cortexa services.

This module provides a single entry point for setting up logging, tracing,
and metrics for microservices in the Cortexa platform.
"""

from typing import Optional, Dict, Any
from fastapi import FastAPI

from .config import MonitoringSettings, get_monitoring_settings
from .tracing import setup_tracing, shutdown_tracing, get_tracer, trace_span
from .metrics import setup_metrics, get_metrics_manager, CommonMetrics
from ..logging import setup_service_logging, get_logger, LogConfig

logger = get_logger(__name__)


class MonitoringManager:
    """Manages all monitoring components for a service."""
    
    def __init__(self, service_name: str, settings: Optional[MonitoringSettings] = None):
        """
        Initialize monitoring manager.
        
        Args:
            service_name: Name of the service
            settings: Monitoring settings (auto-detected if None)
        """
        self.service_name = service_name
        self.settings = settings or get_monitoring_settings(service_name)
        self.common_metrics: Optional[CommonMetrics] = None
        self._initialized = False
    
    def setup_all(self, app: Optional[FastAPI] = None) -> None:
        """
        Set up all monitoring components.
        
        Args:
            app: FastAPI application (required for metrics)
        """
        try:
            # Setup logging first
            self.setup_logging()
            
            # Setup tracing
            self.setup_tracing()
            
            # Setup metrics (requires FastAPI app)
            if app:
                self.setup_metrics(app)
            
            self._initialized = True
            logger.info(
                "Monitoring setup completed",
                service=self.service_name,
                logging_enabled=True,
                tracing_enabled=self.settings.tracing.enabled,
                metrics_enabled=self.settings.metrics.enabled,
            )
            
        except Exception as e:
            logger.error(f"Failed to setup monitoring: {e}")
            raise
    
    def setup_logging(self) -> None:
        """Set up logging with loguru."""
        try:
            log_config = LogConfig(
                service_name=self.service_name,
                level=self.settings.logging.level,
                format_json=self.settings.logging.format.lower() == "json",
                log_file=self.settings.logging.file,
                max_file_size=self.settings.logging.max_file_size,
                retention=self.settings.logging.retention,
                compression=self.settings.logging.compression,
                include_trace=self.settings.logging.include_trace,
                diagnose=self.settings.logging.diagnose,
            )
            
            from ..logging import setup_logging
            setup_logging(log_config)
            
            logger.info("Logging setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup logging: {e}")
            raise
    
    def setup_tracing(self) -> None:
        """Set up distributed tracing."""
        try:
            tracer = setup_tracing(self.settings.tracing)
            if tracer:
                logger.info("Tracing setup completed")
            else:
                logger.info("Tracing disabled or failed to initialize")
                
        except Exception as e:
            logger.error(f"Failed to setup tracing: {e}")
            # Don't raise - tracing failure shouldn't stop the service
    
    def setup_metrics(self, app: FastAPI) -> None:
        """
        Set up Prometheus metrics.
        
        Args:
            app: FastAPI application
        """
        try:
            setup_metrics(app, self.settings.metrics)
            
            # Initialize common metrics
            self.common_metrics = CommonMetrics(self.service_name)
            
            # Set service info
            if self.common_metrics.service_info:
                self.common_metrics.set_service_info({
                    'service': self.service_name,
                    'version': '0.1.0',  # TODO: Get from package version
                    'environment': self.settings.environment,
                })
            
            logger.info("Metrics setup completed")
            
        except Exception as e:
            logger.error(f"Failed to setup metrics: {e}")
            # Don't raise - metrics failure shouldn't stop the service
    
    def get_common_metrics(self) -> Optional[CommonMetrics]:
        """Get common metrics instance."""
        return self.common_metrics
    
    def shutdown(self) -> None:
        """Shutdown all monitoring components."""
        try:
            shutdown_tracing()
            logger.info("Monitoring shutdown completed")
        except Exception as e:
            logger.error(f"Error during monitoring shutdown: {e}")
        finally:
            self._initialized = False
    
    def is_initialized(self) -> bool:
        """Check if monitoring is initialized."""
        return self._initialized


# Global monitoring manager
_monitoring_manager: Optional[MonitoringManager] = None


def setup_monitoring(
    service_name: str,
    app: Optional[FastAPI] = None,
    settings: Optional[MonitoringSettings] = None
) -> MonitoringManager:
    """
    Set up monitoring for a service.
    
    Args:
        service_name: Name of the service
        app: FastAPI application (required for metrics)
        settings: Monitoring settings (auto-detected if None)
        
    Returns:
        MonitoringManager instance
    """
    global _monitoring_manager
    
    _monitoring_manager = MonitoringManager(service_name, settings)
    _monitoring_manager.setup_all(app)
    
    return _monitoring_manager


def get_monitoring_manager() -> Optional[MonitoringManager]:
    """
    Get the global monitoring manager.
    
    Returns:
        MonitoringManager instance if available
    """
    return _monitoring_manager


def shutdown_monitoring() -> None:
    """Shutdown monitoring."""
    global _monitoring_manager
    
    if _monitoring_manager:
        _monitoring_manager.shutdown()
        _monitoring_manager = None


# Convenience functions for quick setup
def setup_service_monitoring(
    service_name: str,
    app: FastAPI,
    **kwargs
) -> MonitoringManager:
    """
    Quick setup for service monitoring with sensible defaults.
    
    Args:
        service_name: Name of the service
        app: FastAPI application
        **kwargs: Additional settings to override
        
    Returns:
        MonitoringManager instance
    """
    # Create settings with overrides
    settings = get_monitoring_settings(service_name)
    
    # Apply any overrides
    for key, value in kwargs.items():
        if hasattr(settings, key):
            setattr(settings, key, value)
        elif hasattr(settings.logging, key):
            setattr(settings.logging, key, value)
        elif hasattr(settings.tracing, key):
            setattr(settings.tracing, key, value)
        elif hasattr(settings.metrics, key):
            setattr(settings.metrics, key, value)
    
    return setup_monitoring(service_name, app, settings)


# Re-export commonly used items
__all__ = [
    "MonitoringManager",
    "MonitoringSettings",
    "setup_monitoring",
    "setup_service_monitoring",
    "get_monitoring_manager",
    "shutdown_monitoring",
    "get_monitoring_settings",
    "CommonMetrics",
    "get_tracer",
    "trace_span",
    "get_logger",
]
