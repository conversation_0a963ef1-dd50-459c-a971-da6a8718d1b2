__version__ = "0.1.0"

# Import monitoring functionality
from .monitoring import (
    setup_monitoring,
    setup_service_monitoring,
    get_monitoring_manager,
    shutdown_monitoring,
    MonitoringManager,
    CommonMetrics,
    get_tracer,
    trace_span,
)

from .logging import (
    setup_service_logging,
    get_logger,
    set_correlation_id,
    set_request_id,
    set_user_id,
    get_correlation_id,
    get_request_id,
    get_user_id,
    clear_context,
)

__all__ = [
    "setup_monitoring",
    "setup_service_monitoring",
    "get_monitoring_manager",
    "shutdown_monitoring",
    "MonitoringManager",
    "CommonMetrics",
    "get_tracer",
    "trace_span",
    "setup_service_logging",
    "get_logger",
    "set_correlation_id",
    "set_request_id",
    "set_user_id",
    "get_correlation_id",
    "get_request_id",
    "get_user_id",
    "clear_context",
]
