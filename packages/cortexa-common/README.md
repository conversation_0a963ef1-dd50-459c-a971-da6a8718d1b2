# Cortexa Common

Common utilities and shared code for Cortexa microservices.

## Features

- Database utilities with async support
- Authentication and authorization utilities
- Configuration management
- Event handling with Kafka
- Base schemas and models
- Repository pattern implementation

## Usage

This package is designed to be used as a dependency in Cortexa microservices to provide shared functionality and maintain consistency across services.

## Installation

This package is installed as a local dependency in Cortexa services:

```toml
[tool.poetry.dependencies]
cortexa-common = {path = "../../packages/cortexa-common", develop = true}
```
