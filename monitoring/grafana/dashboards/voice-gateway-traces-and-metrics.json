{"title": "Voice Gateway: Traces and Metrics", "uid": "voice-gateway-traces-metrics", "schemaVersion": 37, "version": 1, "refresh": "5s", "tags": ["voice-gateway", "monitoring"], "panels": [{"type": "timeseries", "title": "HTTP Request Rate", "id": 1, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 0}, "targets": [{"refId": "A", "expr": "rate(http_requests_total{job=\"voice-gateway\"}[5m])", "legendFormat": "{{method}} {{handler}}"}]}, {"type": "timeseries", "title": "STT Duration (seconds)", "id": 2, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 0}, "targets": [{"refId": "A", "expr": "histogram_quantile(0.95, sum(rate(voice_gateway_stt_duration_seconds_bucket[5m])) by (le, provider))", "legendFormat": "p95 STT ({{provider}})"}]}, {"type": "timeseries", "title": "Translation Duration (seconds)", "id": 3, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "targets": [{"refId": "A", "expr": "histogram_quantile(0.95, sum(rate(voice_gateway_translation_duration_seconds_bucket[5m])) by (le, provider))", "legendFormat": "p95 Translation ({{provider}})"}]}, {"type": "timeseries", "title": "TTS Duration (seconds)", "id": 4, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "targets": [{"refId": "A", "expr": "histogram_quantile(0.95, sum(rate(voice_gateway_tts_duration_seconds_bucket[5m])) by (le, provider))", "legendFormat": "p95 TTS ({{provider}})"}]}, {"type": "stat", "title": "Active Connections", "id": 5, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 14}, "targets": [{"refId": "A", "expr": "voice_gateway_active_connections"}]}, {"type": "stat", "title": "Segments Processed", "id": 6, "datasource": {"type": "prometheus", "uid": "prometheus"}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 14}, "targets": [{"refId": "A", "expr": "sum(voice_gateway_speech_segments_processed_total)"}]}, {"type": "row", "title": "Traces", "id": 7, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}}, {"type": "traces", "title": "Recent Traces", "id": 8, "datasource": {"type": "tempo", "uid": "tempo"}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 20}, "targets": [{"refId": "A", "query": "{service.name=\"voice-gateway\"}", "queryType": "traceql"}], "options": {"showTraceId": true, "showTime": true, "showSpanName": true, "showDuration": true}}]}