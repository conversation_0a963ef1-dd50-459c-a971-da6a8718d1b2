# Voice Gateway Monitoring Stack

This directory contains the complete observability stack for the Voice Gateway service, including metrics collection, distributed tracing, and visualization.

## Components

### 1. Prometheus (Metrics Collection)
- **Port**: 9090
- **Purpose**: Collects and stores time-series metrics from the voice-gateway service
- **Configuration**: `prometheus.yml`

### 2. <PERSON><PERSON> (Visualization)
- **Port**: 3000
- **Purpose**: Visualizes metrics and traces through dashboards
- **Default Credentials**: admin/admin
- **Dashboards**: Pre-configured dashboards for voice-gateway monitoring

### 3. Tempo (Distributed Tracing Backend)
- **Port**: 3200 (API), 4317 (OTLP gRPC), 4318 (OTLP HTTP)
- **Purpose**: Collects and visualizes distributed traces from the S2ST pipeline
- **Integration**: OpenTelemetry traces from voice-gateway via OTLP

### 5. Node Exporter (System Metrics)
- **Port**: 9100
- **Purpose**: Collects system-level metrics (CPU, memory, disk, network)

### 6. Redis Exporter (Redis Metrics)
- **Port**: 9121
- **Purpose**: Collects Redis performance metrics

## Quick Start

1. **Start the monitoring stack**:
   ```bash
   docker-compose -f docker-compose.monitoring.yml up -d
   ```

2. **Configure voice-gateway for monitoring**:
   ```bash
   # In your .env file
   TRACING_ENABLED=true
   OTLP_ENDPOINT=http://localhost:4317
   ENABLE_METRICS=true
   ```

3. **Start the voice-gateway service**:
   ```bash
   poetry run python -m src.main
   ```

4. **Access the monitoring interfaces**:
   - Grafana: http://localhost:3000 (admin/admin)
   - Prometheus: http://localhost:9090
   - Tempo: http://localhost:3200

## Dashboards

### Voice Gateway Overview
- **File**: `dashboards/voice-gateway-overview.json`
- **Metrics**:
  - HTTP request rates and response times
  - Active WebSocket connections
  - Audio processing throughput
  - Error rates and types

### S2ST Pipeline Performance
- **Metrics**:
  - STT processing latency and confidence scores
  - Translation processing times
  - TTS generation latency
  - End-to-end pipeline latency

### System Resources
- **Metrics**:
  - CPU and memory usage
  - Network I/O
  - Disk usage
  - Redis performance

## Key Metrics

### Connection Metrics
- `voice_gateway_active_connections`: Current active WebSocket connections
- `voice_gateway_total_connections`: Total connections created
- `voice_gateway_audio_bytes_received_total`: Audio data received
- `voice_gateway_audio_bytes_sent_total`: Audio data sent

### Processing Metrics
- `voice_gateway_speech_segments_processed_total`: Processed speech segments
- `voice_gateway_speech_processing_duration_seconds`: Processing time per stage
- `voice_gateway_stt_duration_seconds`: STT processing time
- `voice_gateway_translation_duration_seconds`: Translation processing time
- `voice_gateway_tts_duration_seconds`: TTS processing time

### Quality Metrics
- `voice_gateway_stt_confidence`: STT confidence scores
- `voice_gateway_processing_errors_total`: Processing errors by type

## Tracing

### Trace Spans
The voice-gateway creates detailed trace spans for:

1. **s2st.process_speech_segment**: Complete speech processing pipeline
2. **s2st.stt**: Speech-to-text conversion
3. **s2st.translation**: Text translation
4. **s2st.tts**: Text-to-speech synthesis
5. **whisper_stt.transcribe**: Whisper-specific STT processing
6. **deepgram_stt.transcribe**: DeepGram-specific STT processing

### Trace Attributes
- `call_id`: Unique call identifier
- `audio_duration_seconds`: Audio segment duration
- `audio_size_bytes`: Audio data size
- `stt.confidence`: STT confidence score
- `transcription.text_length`: Transcribed text length
- `translation.source_length`: Source text length
- `translation.target_length`: Translated text length

## Alerting

### Recommended Alerts
1. **High Error Rate**: Error rate > 5% for 5 minutes
2. **High Latency**: P95 processing time > 10 seconds
3. **Connection Limit**: Active connections > 80% of limit
4. **Low STT Confidence**: Average confidence < 0.7 for 10 minutes
5. **Service Down**: No metrics received for 1 minute

### Alert Configuration
Add alert rules to `prometheus.yml` or configure in Grafana.

## Troubleshooting

### Common Issues

1. **No metrics appearing**:
   - Check if `ENABLE_METRICS=true` in voice-gateway
   - Verify Prometheus can reach voice-gateway:8002/metrics
   - Check Docker network connectivity

2. **No traces appearing**:
   - Verify `TRACING_ENABLED=true` in voice-gateway
   - Check Tempo endpoint is accessible
   - Ensure OpenTelemetry OTLP exporter is configured

3. **Grafana dashboards not loading**:
   - Check datasource configuration
   - Verify Prometheus/Jaeger connectivity
   - Check dashboard JSON syntax

### Logs
Check container logs for issues:
```bash
docker-compose -f docker-compose.monitoring.yml logs -f [service-name]
```

## Scaling

For production deployments:
1. Use external storage for Prometheus (not local volumes)
2. Configure Grafana with external database
3. Set up Prometheus federation for multiple instances
4. Use Tempo with object storage backend
5. Configure proper retention policies
