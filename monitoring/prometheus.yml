global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Voice Gateway service metrics
  - job_name: 'voice-gateway'
    static_configs:
      - targets: ['host.docker.internal:8002']
    metrics_path: '/metrics'
    scrape_interval: 5s
    scrape_timeout: 5s

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # FastAPI metrics (if using prometheus-fastapi-instrumentator)
  - job_name: 'voice-gateway-app'
    static_configs:
      - targets: ['host.docker.internal:8002']
    metrics_path: '/metrics'
    scrape_interval: 5s
