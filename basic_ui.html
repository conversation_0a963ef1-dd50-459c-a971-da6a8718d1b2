<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Gateway WebSocket Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.error { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log {
            background: #000;
            color: #00ff00;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            border-radius: 4px;
        }
        .audio-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <h1>Voice Gateway WebSocket Test Client</h1>
    
    <div class="container">
        <h3>Connection Settings</h3>
        <div>
            <label>WebSocket URL:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8002" style="width: 200px;">
        </div>
        <div>
            <label>JWT Token:</label>
            <input type="text" id="jwtToken" placeholder="Enter JWT token or leave empty for test token" style="width: 400px;">
        </div>
        <div>
            <label>Call ID:</label>
            <input type="text" id="callId" style="width: 200px;">
        </div>
        <div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>
        <div id="connectionStatus" class="status disconnected">Disconnected</div>
    </div>

    <div class="container">
        <h3>Control Messages</h3>
        <div>
            <button onclick="sendPing()" disabled id="pingBtn">Send Ping</button>
            <button onclick="sendConfigUpdate()" disabled id="configBtn">Update Config</button>
            <button onclick="endCall()" disabled id="endCallBtn">End Call</button>
        </div>
        <div>
            <label>Target Language:</label>
            <select id="targetLang">
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="it">Italian</option>
            </select>
        </div>
    </div>

    <div class="container">
        <h3>Audio Recording</h3>
        <div class="audio-controls">
            <button onclick="startRecording()" disabled id="recordBtn">Start Recording</button>
            <button onclick="stopRecording()" disabled id="stopBtn">Stop Recording</button>
        </div>
        <div>
            <audio id="audioPlayback" controls style="width: 100%; margin-top: 10px;"></audio>
        </div>
    </div>

    <div class="container">
        <h3>Translation Results</h3>
        <div id="translationResults" style="background: #f8f9fa; padding: 15px; border-radius: 4px; min-height: 200px; max-height: 300px; overflow-y: auto; font-family: Arial, sans-serif;">
            <div style="color: #6c757d; font-style: italic;">Translation results will appear here...</div>
        </div>
    </div>

    <div class="container">
        <h3>Message Log</h3>
        <div id="messageLog" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let websocket = null;
        let audioContext = null;
        let scriptProcessor = null;
        let mediaStreamSource = null;

        // Generate a random call ID
        document.getElementById('callId').value = 'test-call-' + Math.random().toString(36).substr(2, 9);

        function log(message, type = 'info') {
            console.log(message);
            const logDiv = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'sent' ? '#4ecdc4' : '#00ff00';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(status, message) {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;

            const isConnected = status === 'connected';
            document.getElementById('connectBtn').disabled = isConnected;
            document.getElementById('disconnectBtn').disabled = !isConnected;
            document.getElementById('pingBtn').disabled = !isConnected;
            document.getElementById('configBtn').disabled = !isConnected;
            document.getElementById('endCallBtn').disabled = !isConnected;
            document.getElementById('recordBtn').disabled = !isConnected;
            document.getElementById('stopBtn').disabled = true; // Initially disabled
        }

        function addTranslationResult(originalText, translatedText, confidence) {
            const resultsDiv = document.getElementById('translationResults');
            const timestamp = new Date().toLocaleTimeString();

            // Clear the placeholder text if it's the first result
            if (resultsDiv.children.length === 1 && resultsDiv.children[0].style.fontStyle === 'italic') {
                resultsDiv.innerHTML = '';
            }

            const resultDiv = document.createElement('div');
            resultDiv.style.cssText = 'margin-bottom: 15px; padding: 10px; border-left: 3px solid #007bff; background: white; border-radius: 4px;';

            resultDiv.innerHTML = `
                <div style="font-size: 12px; color: #6c757d; margin-bottom: 5px;">${timestamp} (Confidence: ${(confidence * 100).toFixed(1)}%)</div>
                <div style="margin-bottom: 8px;"><strong>Original:</strong> ${originalText}</div>
                <div style="color: #007bff;"><strong>Translation:</strong> ${translatedText}</div>
            `;

            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function generateTestToken() {
            try {
                const authUrl = 'http://localhost:8001/api/v1/auth/login';
                const response = await fetch(authUrl, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({ username: 'admin', password: 'Admin123!' })
                });
                if (response.ok) {
                    const data = await response.json();
                    log('Successfully obtained real JWT token from auth service', 'info');
                    return data.data.access_token;
                }
            } catch (error) {
                log(`Auth service not available: ${error.message}`, 'info');
            }
            log('Using test token', 'info');
            return 'test-token';
        }

        async function connect() {
            const wsUrl = document.getElementById('wsUrl').value;
            const callId = document.getElementById('callId').value;
            let token = document.getElementById('jwtToken').value;

            if (!token) {
                log('No token provided, attempting to get one...', 'info');
                token = await generateTestToken();
                if (!token) {
                    log('Failed to obtain JWT token.', 'error');
                    return;
                }
            }

            const fullUrl = `${wsUrl}/api/v1/call/ws/call/${callId}?token=${token}`;
            
            try {
                log(`Connecting to: ${fullUrl}`, 'info');
                websocket = new WebSocket(fullUrl);
                
                websocket.onopen = (event) => {
                    log('WebSocket connection established', 'info');
                    updateConnectionStatus('connected', 'Connected');
                };
                
                websocket.onmessage = (event) => {
                    if (event.data instanceof Blob) {
                        log(`Received translated audio: ${event.data.size} bytes`, 'info');
                        const audioUrl = URL.createObjectURL(event.data);
                        const audioElement = document.getElementById('audioPlayback');
                        audioElement.src = audioUrl;
                        audioElement.play().catch(e => log(`Audio playback error: ${e.message}`, 'error'));
                    } else {
                        try {
                            const message = JSON.parse(event.data);
                            log(`Received: ${JSON.stringify(message, null, 2)}`, 'info');

                            // Handle translation results
                            if (message.type === 'translation_result') {
                                addTranslationResult(
                                    message.original_text || 'N/A',
                                    message.translated_text || 'N/A',
                                    message.confidence || 0
                                );
                            } else if (message.type === 'config_updated') {
                                log(`Configuration updated: ${JSON.stringify(message.config)}`, 'info');
                            }
                        } catch (e) {
                            log(`Received non-JSON message: ${event.data}`, 'info');
                        }
                    }
                };
                
                websocket.onerror = (error) => {
                    log('WebSocket error', 'error');
                    console.error(error);
                    updateConnectionStatus('error', 'Connection Error');
                };
                
                websocket.onclose = (event) => {
                    log(`WebSocket closed: ${event.code} - ${event.reason}`, 'info');
                    updateConnectionStatus('disconnected', 'Disconnected');
                    stopRecording(); // Ensure recording stops on disconnect
                    websocket = null;
                };
                
            } catch (error) {
                log(`Connection failed: ${error.message}`, 'error');
                updateConnectionStatus('error', 'Connection Failed');
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                log('Disconnecting...', 'info');
            }
            stopRecording();
        }

        // --- IMPROVED AUDIO RECORDING LOGIC ---

        async function startRecording() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                log('Connect WebSocket before recording.', 'error');
                return;
            }
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: false
                    }
                });

                // Create AudioContext without forcing sample rate - let it use the system default
                audioContext = new (window.AudioContext || window.webkitAudioContext)();

                log(`AudioContext created with sample rate: ${audioContext.sampleRate}Hz`, 'info');

                mediaStreamSource = audioContext.createMediaStreamSource(stream);

                // Use a smaller buffer size for lower latency
                // Buffer size should be power of 2: 256, 512, 1024, 2048, 4096
                scriptProcessor = audioContext.createScriptProcessor(1024, 1, 1);

                scriptProcessor.onaudioprocess = (event) => {
                    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                        return;
                    }
                    const inputData = event.inputBuffer.getChannelData(0);
                    const actualSampleRate = audioContext.sampleRate;

                    // Resample to 16kHz if needed
                    let processedData = inputData;
                    if (actualSampleRate !== 16000) {
                        processedData = resampleAudio(inputData, actualSampleRate, 16000);
                    }

                    // Convert Float32 data to 16-bit PCM with proper clamping
                    const buffer = new ArrayBuffer(processedData.length * 2);
                    const view = new DataView(buffer);
                    for (let i = 0; i < processedData.length; i++) {
                        // Clamp to [-1, 1] range and convert to 16-bit signed integer
                        const sample = Math.max(-1, Math.min(1, processedData[i]));
                        const intSample = Math.round(sample * 32767);
                        view.setInt16(i * 2, intSample, true); // little-endian
                    }

                    websocket.send(buffer);
                };

                mediaStreamSource.connect(scriptProcessor);
                scriptProcessor.connect(audioContext.destination);

                document.getElementById('recordBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                log(`Started real-time audio streaming at ${audioContext.sampleRate}Hz...`, 'info');

            } catch (error) {
                log(`Recording error: ${error.message}`, 'error');
            }
        }

        // Simple linear resampling function
        function resampleAudio(inputBuffer, inputSampleRate, outputSampleRate) {
            if (inputSampleRate === outputSampleRate) {
                return inputBuffer;
            }

            const ratio = inputSampleRate / outputSampleRate;
            const outputLength = Math.round(inputBuffer.length / ratio);
            const outputBuffer = new Float32Array(outputLength);

            for (let i = 0; i < outputLength; i++) {
                const inputIndex = i * ratio;
                const inputIndexFloor = Math.floor(inputIndex);
                const inputIndexCeil = Math.min(inputIndexFloor + 1, inputBuffer.length - 1);
                const fraction = inputIndex - inputIndexFloor;

                // Linear interpolation
                outputBuffer[i] = inputBuffer[inputIndexFloor] * (1 - fraction) +
                                 inputBuffer[inputIndexCeil] * fraction;
            }

            return outputBuffer;
        }

        function stopRecording() {
            if (scriptProcessor) {
                scriptProcessor.disconnect();
                scriptProcessor = null;
            }
            if (mediaStreamSource) {
                mediaStreamSource.mediaStream.getTracks().forEach(track => track.stop());
                mediaStreamSource.disconnect();
                mediaStreamSource = null;
            }
            if (audioContext) {
                audioContext.close();
                audioContext = null;
            }
            document.getElementById('recordBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            log('Stopped audio streaming', 'info');
        }
        
        // Control functions
        function sendPing() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify({type: "ping"}));
                log('Sent: {"type":"ping"}', 'sent');
            }
        }

        function sendConfigUpdate() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const lang = document.getElementById('targetLang').value;
                const msg = {type: "config_update", config: {target_language: lang}};
                websocket.send(JSON.stringify(msg));
                log(`Sent: ${JSON.stringify(msg)}`, 'sent');
            }
        }

        function endCall() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify({type: "end_call"}));
                log('Sent: {"type":"end_call"}', 'sent');
            }
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }
        updateConnectionStatus('disconnected', 'Disconnected');
    </script>
</body>
</html>
